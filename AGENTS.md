# Repository Guidelines

## Project Structure & Module Organization

- Entry chain: `src/main.tsx` → `src/provider.tsx` → `src/App.tsx`; all routing flows through the provider helpers.
- Feature screens live in `src/pages/<feature>/`, with reusable pieces in `src/components/<Feature>/index.ts` for clean exports.
- Redux Toolkit setup is found in `src/store/` (slices, store, typed hooks); layouts sit in `src/layouts/`.
- Shared assets: contracts in `src/types/`, helpers in `src/utils/`, theme tweaks in `src/styles/`.
- Co-locate tests as `*.test.tsx` beside the implementation.

## Build, Test, and Development Commands

- `npm run dev` — Vite dev server with HMR.
- `npm run build` — run `tsc` type checks, then create the production bundle.
- `npm run preview` — serve the built bundle for smoke tests.
- `npm run lint` / `npm run format` — apply ESLint (`--fix`) and Prettier before commits.
- Run tests by executing `npm run build`.

## Coding Style & Naming Conventions

- TypeScript strict mode; favor functional React components and typed hooks.
- Import order: React → third-party → blank line → `@/` aliases.
- Naming: PascalCase components, camelCase hooks/utilities, SCREAMING_SNAKE_CASE constants.
- Styling: Tailwind v4 utilities and HeroUI props; reach for `tailwind-variants` before custom CSS.
- Formatting handled by Prettier (2-space indent, semicolons); ESLint enforces React, hooks, and unused-import rules.

## Testing Guidelines

- Use Vitest with Testing Library assertions; simulate Redux and router context via helpers in `src/store/` and `src/provider.tsx`.
- Mirror filenames (`FeaturePanel.test.tsx`) and keep specs focused on observable behavior.
- Mock Firebase and REST clients; prefer dependency injection over jest.mock globals.
- Validate async flows remain serializable for Redux middleware and cover guarded routes (`src/components/route/`).

## Commit & Pull Request Guidelines

- Follow Conventional Commits (`feat(chat): ...`, `fix(auth): ...`) as demonstrated in history; keep scope tight.
- PRs should explain intent, list key changes, reference issues, and attach UI screenshots when visuals change.
- Confirm lint, format, build, and relevant tests succeed; note any skipped steps and mitigation.
- Request reviewer sign-off for updates touching routing guards, state slices, or shared types.

## Environment & Security Notes

- Secrets live in `.env.local` with `VITE_` prefixes; never commit Firebase keys or project IDs.
- New services belong in `src/services/` and should reuse `baseServiceEndpoint.ts`; document added env vars in the PR description.
