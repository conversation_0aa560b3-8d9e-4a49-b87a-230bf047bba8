# Label API Endpoint Documentation

## Overview

The Label API allows users to manage labels within a project. Labels can be used to categorize or tag various items within a project.

## Endpoints

### Get All Labels

- **Method**: `GET`
- **Path**: `/user/project/:projectId/label`
- **Description**: Retrieves all labels associated with a specific project
- **Parameters**:
  - `projectId` (path parameter): Unique identifier of the project
- **Headers**:
  - `Authorization`: Bearer token for authentication
- **Response**:
  - Success (200):
    ```json
    {
      "message": "Successfully get all labels",
      "data": [
        {
          "id": "string",
          "name": "string",
          "description": "string",
          "createdAt": "Date",
          "updatedAt": "Date"
        }
      ]
    }
    ```

### Create Label

- **Method**: `POST`
- **Path**: `/user/project/:projectId/label`
- **Description**: Creates a new label within a specific project
- **Parameters**:
  - `projectId` (path parameter): Unique identifier of the project
- **Headers**:
  - `Authorization`: Bearer token for authentication
- **Request Body**:
  ```json
  {
    "name": "string",
    "description": "string"
  }
  ```
- **Validation**:
  - `name` is required and must not be empty
  - `description` is optional
- **Response**:
  - Success (201):
    ```json
    {
      "message": "Successfully created label",
      "data": {
        "id": "string",
        "name": "string",
        "description": "string",
        "createdAt": "Date",
        "updatedAt": "Date"
      }
    }
    ```

### Update Label

- **Method**: `PUT`
- **Path**: `/user/project/:projectId/label/:labelId`
- **Description**: Updates an existing label within a specific project
- **Parameters**:
  - `projectId` (path parameter): Unique identifier of the project
  - `labelId` (path parameter): Unique identifier of the label to update
- **Headers**:
  - `Authorization`: Bearer token for authentication
- **Request Body**:
  ```json
  {
    "name": "string",
    "description": "string"
  }
  ```
- **Validation**:
  - `name` is optional but if provided must not be empty
  - `description` is optional
- **Response**:
  - Success (200):
    ```json
    {
      "message": "Successfully updated label",
      "data": {
        "id": "string",
        "name": "string",
        "description": "string",
        "createdAt": "Date",
        "updatedAt": "Date"
      }
    }
    ```

### Delete Label

- **Method**: `DELETE`
- **Path**: `/user/project/:projectId/label/:labelId`
- **Description**: Deletes an existing label within a specific project
- **Parameters**:
  - `projectId` (path parameter): Unique identifier of the project
  - `labelId` (path parameter): Unique identifier of the label to delete
- **Headers**:
  - `Authorization`: Bearer token for authentication
- **Response**:
  - Success (200):
    ```json
    {
      "message": "Successfully deleted label",
      "data": {
        "id": "string"
      }
    }
    ```

## Data Models

### Label Object

| Field       | Type   | Description                               |
| ----------- | ------ | ----------------------------------------- |
| id          | string | Unique identifier of the label            |
| name        | string | Name of the label                         |
| description | string | Description of the label (optional)       |
| createdAt   | Date   | Timestamp when the label was created      |
| updatedAt   | Date   | Timestamp when the label was last updated |

### Request Body (Create Label)

| Field       | Type   | Required | Description                           |
| ----------- | ------ | -------- | ------------------------------------- |
| name        | string | Yes      | Name of the label (must not be empty) |
| description | string | No       | Description of the label              |

### Request Body (Update Label)

| Field       | Type   | Required | Description                                           |
| ----------- | ------ | -------- | ----------------------------------------------------- |
| name        | string | No       | New name of the label (must not be empty if provided) |
| description | string | No       | New description of the label                          |

### Response Format

All responses follow the same structure:

```json
{
  "message": "string",
  "data": {}
}
```

## Error Handling

- Unauthorized access will be handled by the `AuthGuard`
- Invalid project access will be handled by the `ProjectGuard`
- Validation errors will be handled by Zod validation in the DTO
- Invalid name (empty) will return a validation error
- Access to non-existent labels will return an error

## Notes

- All endpoints require a valid project ID in the URL path
- Labels are scoped to a specific project and cannot be accessed from other projects
- The `createdAt` and `updatedAt` fields are automatically managed by the service
- The `updatedAt` field is updated automatically when a label is modified
