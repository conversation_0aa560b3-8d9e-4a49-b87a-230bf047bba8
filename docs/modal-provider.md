# Modal Provider

Sistem modal global menggunakan HeroUI yang menyediakan tiga jenis modal: Confirm, <PERSON><PERSON><PERSON>, dan <PERSON>, dengan integrasi Context API untuk state management dan custom hooks untuk penggunaan yang mudah.

## <PERSON><PERSON>

[text](../../../nodejs/ideal-public-backend/docs/api/label-endpoint.md)

1. **Confirm Modal** - Untuk meminta konfirmasi dari pengguna sebelum melakukan tindakan
2. **Error Modal** - Untuk menampilkan pesan kesalahan
3. **Success Modal** - Untuk menampilkan pesan keberhasilan
4. **Info Modal** - Untuk menampilkan pesan informasi

## Cara Menggunakan

### 1. Di dalam komponen React

```tsx
import { useModal } from "@/hooks/useModal";

const MyComponent = () => {
  const { showConfirm, showError, showSuccess } = useModal();

  const handleAction = () => {
    showConfirm({
      title: "<PERSON>nfirma<PERSON>",
      message: "<PERSON><PERSON><PERSON><PERSON> Anda yakin ingin melanjutkan?",
      onConfirm: () => {
        console.log("User confirmed");
      },
      onCancel: () => {
        console.log("User cancelled");
      },
      confirmText: "Ya",
      cancelText: "Tidak",
    });
  };

  return (
    <div>
      <button onClick={handleAction}>Lakukan Aksi</button>
    </div>
  );
};
```

### 2. Modal dengan Auto Close

```tsx
const handleAutoClose = () => {
  showSuccess({
    title: "Berhasil",
    message: "Operasi berhasil dilakukan.",
    autoCloseTimer: 3000, // Modal akan tertutup otomatis dalam 3 detik
    onConfirm: () => {
      console.log("Success acknowledged");
    },
  });
};
```

## Tipe Data

### ModalType

```ts
type ModalType = "confirm" | "error" | "success" | "info";
```

### BaseModalConfig

- `title: string` - Judul modal
- `message: string` - Pesan yang ditampilkan dalam modal
- `onClose?: () => void` - Callback saat modal ditutup
- `autoCloseTimer?: number` - Timer untuk penutupan otomatis (dalam milidetik)

### ConfirmModalConfig

- Semua properti dari `BaseModalConfig`
- `onConfirm?: () => void` - Callback saat tombol konfirmasi ditekan
- `onCancel?: () => void` - Callback saat tombol batal ditekan
- `confirmText?: string` - Teks untuk tombol konfirmasi (default: 'OK')
- `cancelText?: string` - Teks untuk tombol batal (default: 'Cancel')

### ErrorModalConfig & SuccessModalConfig

- Semua properti dari `BaseModalConfig`
- `onConfirm?: () => void` - Callback saat tombol ditekan
- `confirmText?: string` - Teks untuk tombol konfirmasi (default: 'OK')

### InfoModalConfig

- Semua properti dari `BaseModalConfig`
- `onConfirm?: () => void` - Callback saat tombol ditekan
- `confirmText?: string` - Teks untuk tombol konfirmasi (default: 'OK')

## Fungsi yang Tersedia

- `showConfirm(config: ConfirmModalConfig)` - Menampilkan modal konfirmasi
- `showError(config: ErrorModalConfig)` - Menampilkan modal error
- `showSuccess(config: SuccessModalConfig)` - Menampilkan modal sukses
- `showInfo(config: InfoModalConfig)` - Menampilkan modal informasi
- `closeModal()` - Menutup modal yang sedang terbuka
- `useModal()` - Custom hook untuk mengakses fungsi-fungsi modal

## Integrasi

ModalProvider sudah diintegrasikan ke dalam aplikasi melalui file `src/provider.tsx`, sehingga dapat digunakan di seluruh bagian aplikasi.
