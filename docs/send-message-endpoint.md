# WhatsApp Message API Documentation

## 📖 Overview

The WhatsApp Message API allows you to send various types of messages (text, images, audio, video, documents, interactive, and templates) to WhatsApp users through our platform.

### Key Features

- **Multiple Message Types**: Support for text, media, interactive, and template messages
- **Context-based**: Messages are sent within specific projects and chats
- **RESTful Design**: Simple HTTP POST requests with JSON payloads
- **Comprehensive Error Handling**: Clear error responses for debugging

### Base Endpoint

```http
POST /user/project/:projectId/chat/:chatId/messages
```

## 🔐 Authentication

All API requests must include a valid authentication token in the request headers.

### Required Headers

```http
Authorization: Bearer your-auth-token
Content-Type: application/json
```

## 📋 Request Parameters

### Path Parameters

| Parameter   | Type   | Required | Description                                       |
| ----------- | ------ | -------- | ------------------------------------------------- |
| `projectId` | string | ✅ Yes   | Unique identifier for the project                 |
| `chatId`    | string | ✅ Yes   | Unique identifier for the chat within the project |

### Required Headers

| Header          | Type   | Required | Description                     |
| --------------- | ------ | -------- | ------------------------------- |
| `Authorization` | string | ✅ Yes   | Bearer token for authentication |
| `Content-Type`  | string | ✅ Yes   | Request content type            |

## 💬 Message Types & Request Body

You must send a JSON object with different structures depending on the message type:

### 1. Text Message

**Use for:** Simple text messages

```json
{
  "type": "text",
  "text": {
    "body": "Hello! This is a text message.",
    "previewUrl": false
  }
}
```

**Properties:**

- `body`: The message text (max 4096 characters)
- `previewUrl`: Whether to generate URL previews (default: false)

**Example:**

```json
{
  "type": "text",
  "text": {
    "body": "Welcome to our service! How can we help you today?",
    "previewUrl": true
  }
}
```

### 2. Image Message

**Use for:** Sending images with optional captions

```json
{
  "type": "image",
  "image": {
    "id": "media-id-optional", // Either id or link is required
    "link": "https://example.com/image.jpg", // Either id or link is required
    "caption": "Optional: Image description"
  }
}
```

**Properties:**

- `id` OR `link` (required): Media ID from uploaded file or public URL
- `caption` (optional): Image description text

**Notes:**

- Use `id` for previously uploaded media (via media management)
- Use `link` for publicly accessible image URLs
- Supported formats: JPG, PNG, GIF (max 5MB)
- Max caption length: 1024 characters

**Example with link:**

```json
{
  "type": "image",
  "image": {
    "link": "https://example.com/product-image.jpg",
    "caption": "Check out our new product!"
  }
}
```

### 3. Audio Message

**Use for:** Sending audio files

```json
{
  "type": "audio",
  "audio": {
    "id": "media-id-optional", // Either id or link is required
    "link": "https://example.com/audio.mp3" // Either id or link is required
  }
}
```

**Properties:**

- `id` OR `link` (required): Media ID or public URL

**Notes:**

- Supported formats: MP3, WAV, OGG (max 16MB)
- Audio messages play automatically when received

**Example:**

```json
{
  "type": "audio",
  "audio": {
    "link": "https://example.com/voice-message.mp3"
  }
}
```

### 4. Video Message

**Use for:** Sending video files with optional captions

```json
{
  "type": "video",
  "video": {
    "id": "media-id-optional", // Either id or link is required
    "link": "https://example.com/video.mp4", // Either id or link is required
    "caption": "Optional: Video description"
  }
}
```

**Properties:**

- `id` OR `link` (required): Media ID or public URL
- `caption` (optional): Video description text

**Notes:**

- Supported formats: MP4, 3GP (max 16MB)
- Max caption length: 1024 characters

**Example:**

```json
{
  "type": "video",
  "video": {
    "link": "https://example.com/tutorial-video.mp4",
    "caption": "Here's a quick tutorial for you!"
  }
}
```

### 5. Document Message

**Use for:** Sending documents like PDFs, Word files, etc.

```json
{
  "type": "document",
  "document": {
    "id": "media-id-optional", // Either id or link is required
    "link": "https://example.com/document.pdf", // Either id or link is required
    "caption": "Optional: Document description",
    "filename": "document-name.pdf" // Display filename
  }
}
```

**Properties:**

- `id` OR `link` (required): Media ID or public URL
- `caption` (optional): Document description
- `filename` (required): Display name for the file

**Notes:**

- Supported formats: PDF, DOC, DOCX, PPT, PPTX, XLS, XLSX (max 100MB)
- Filename should include file extension

**Example:**

```json
{
  "type": "document",
  "document": {
    "link": "https://example.com/invoice-2023.pdf",
    "caption": "Your monthly invoice is ready",
    "filename": "Invoice_March_2023.pdf"
  }
}
```

### 6. Interactive Message

**Use for:** Messages with buttons, lists, or interactive elements

```json
{
  "type": "interactive",
  "interactive": {
    "type": "button|list|product|product_list",
    "body": {
      "text": "Main message content"
    },
    "action": {
      "buttons": [
        {
          "type": "reply",
          "reply": {
            "id": "button-id",
            "title": "Button Title"
          }
        }
      ],
      "button": "Button Title",
      "sections": [
        {
          "title": "Section Title",
          "rows": [
            {
              "id": "row-id",
              "title": "Row Title",
              "description": "Row Description"
            }
          ]
        }
      ]
    }
  }
}
```

**Interactive Types:**

- `button`: Quick reply buttons (max 3 buttons)
- `list`: Selection lists (max 10 items)
- `product`: Product showcase
- `product_list`: Multiple product showcase

**Example with buttons:**

```json
{
  "type": "interactive",
  "interactive": {
    "type": "button",
    "body": {
      "text": "How can we help you today?"
    },
    "action": {
      "buttons": [
        {
          "type": "reply",
          "reply": {
            "id": "help_support",
            "title": "Get Support"
          }
        },
        {
          "type": "reply",
          "reply": {
            "id": "view_products",
            "title": "View Products"
          }
        }
      ]
    }
  }
}
```

### 7. Template Message

**Use for:** Sending pre-approved message templates

```json
{
  "type": "template",
  "template": {
    "name": "template-name",
    "language": {
      "code": "en_US", // ISO language code
      "policy": "deterministic"
    },
    "components": [
      {
        "type": "body",
        "parameters": [
          {
            "type": "text",
            "text": "First parameter value"
          }
        ]
      }
    ]
  }
}
```

**Properties:**

- `name`: Template name (must be pre-approved)
- `language`: Language settings
  - `code`: ISO language code (e.g., `en_US`, `id_ID`)
  - `policy`: Always use `"deterministic"`
- `components`: Template components (body, header, buttons)

**Important Notes:**

- Templates must be pre-approved in your WhatsApp Business account
- Templates can only be used for customer-initiated conversations
- Template messages are essential for notifications and important updates

**Example:**

```json
{
  "type": "template",
  "template": {
    "name": "order_confirmation",
    "language": {
      "code": "en_US",
      "policy": "deterministic"
    },
    "components": [
      {
        "type": "body",
        "parameters": [
          {
            "type": "text",
            "text": "John Doe"
          },
          {
            "type": "text",
            "text": "12345"
          }
        ]
      }
    ]
  }
}
```

## 📤 Response

### Success Response (201 Created)

When the message is successfully sent, you'll receive a response like this:

```json
{
  "message": "Successfully sent message",
  "data": {
    "message": {
      "id": "wamid.************************************************************",
      "from": "***********",
      "timestamp": "2023-01-01T00:00:00.000Z",
      "type": "text",
      "context": null,
      "text": {
        "body": "Hello! This is a test message."
      },
      "statuses": null,
      "direction": "out",
      "createdAt": "2023-01-01T00:00:00.000Z",
      "sentBy": {
        "uid": "user-123",
        "email": "<EMAIL>",
        "name": "John Doe"
      }
    }
  }
}
```

**Response Properties:**

- `message`: Success message
- `data.message`: Complete message object with WhatsApp metadata
- `data.message.id`: WhatsApp message ID (use this for status tracking)
- `data.message.type`: Message type sent
- `data.sentBy`: User who sent the message

### ❌ Error Responses

| Status Code | Error Type            | Description              | Common Causes                                                                       |
| ----------- | --------------------- | ------------------------ | ----------------------------------------------------------------------------------- |
| 400         | Bad Request           | Invalid request format   | - Missing required fields<br>- Invalid JSON structure<br>- Unsupported message type |
| 401         | Unauthorized          | Authentication failed    | - Invalid or expired token<br>- Missing Authorization header                        |
| 403         | Forbidden             | Insufficient permissions | - User lacks project access<br>- Chat is archived/inactive                          |
| 404         | Not Found             | Resource not found       | - Invalid projectId/chatId<br>- Project or chat doesn't exist                       |
| 422         | Unprocessable Entity  | Validation failed        | - Invalid media format<br>- File size exceeded<br>- Invalid template parameters     |
| 500         | Internal Server Error | Server processing failed | - WhatsApp API error<br>- Database connection issue<br>- Temporary server issue     |

**Error Response Format:**

```json
{
  "message": "Error description",
  "error": "ERROR_TYPE",
  "details": {
    "field": "Specific error details"
  }
}
```

**Common Error Examples:**

```json
// 400 - Invalid Message Type
{
  "message": "Invalid message type: 'invalid_type'",
  "error": "VALIDATION_ERROR"
}

// 401 - Unauthorized
{
  "message": "Invalid or expired authentication token",
  "error": "UNAUTHORIZED"
}

// 404 - Not Found
{
  "message": "Chat not found",
  "error": "NOT_FOUND"
}
```

## 🚀 Implementation Examples

### JavaScript (Fetch API)

```javascript
// Base configuration
const BASE_URL = "https://api.yourdomain.com";
const AUTH_TOKEN = "your-auth-token-here";

// Generic message sender function
class WhatsAppMessageService {
  constructor(baseUrl, authToken) {
    this.baseUrl = baseUrl;
    this.authToken = authToken;
  }

  async sendMessage(projectId, chatId, messageData) {
    try {
      const response = await fetch(
        `${this.baseUrl}/user/project/${projectId}/chat/${chatId}/messages`,
        {
          method: "POST",
          headers: {
            Authorization: `Bearer ${this.authToken}`,
            "Content-Type": "application/json",
          },
          body: JSON.stringify(messageData),
        },
      );

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(
          `HTTP ${response.status}: ${errorData.message || response.statusText}`,
        );
      }

      const result = await response.json();
      return {
        success: true,
        data: result.data,
        messageId: result.data.message.id,
      };
    } catch (error) {
      console.error("Message sending failed:", error);
      return {
        success: false,
        error: error.message,
        data: null,
      };
    }
  }

  // Convenience methods for different message types
  async sendText(projectId, chatId, text, previewUrl = false) {
    return this.sendMessage(projectId, chatId, {
      type: "text",
      text: { body: text, previewUrl },
    });
  }

  async sendImage(projectId, chatId, imageUrl, caption = "") {
    return this.sendMessage(projectId, chatId, {
      type: "image",
      image: { link: imageUrl, caption },
    });
  }

  async sendDocument(projectId, chatId, documentUrl, filename, caption = "") {
    return this.sendMessage(projectId, chatId, {
      type: "document",
      document: { link: documentUrl, filename, caption },
    });
  }
}

// Usage example
const whatsappService = new WhatsAppMessageService(BASE_URL, AUTH_TOKEN);

// Example 1: Send text message
const sendWelcomeMessage = async (projectId, chatId, userName) => {
  const result = await whatsappService.sendText(
    projectId,
    chatId,
    `Welcome ${userName}! How can we assist you today?`,
  );

  if (result.success) {
    console.log("Welcome message sent:", result.messageId);
    // Store messageId for tracking
    return result.messageId;
  } else {
    console.error("Failed to send welcome message:", result.error);
    // Handle error (show notification, retry, etc.)
    throw new Error(result.error);
  }
};

// Example 2: Send order confirmation with document
const sendOrderConfirmation = async (projectId, chatId, orderData) => {
  const result = await whatsappService.sendDocument(
    projectId,
    chatId,
    orderData.invoiceUrl,
    `Invoice_${orderData.orderId}.pdf`,
    `Thank you for your order! Your invoice is attached.`,
  );

  return result;
};

// Example usage
const projectId = "proj_123456";
const chatId = "chat_789012";

sendWelcomeMessage(projectId, chatId, "John Doe")
  .then((messageId) => {
    console.log("Message sent successfully with ID:", messageId);
  })
  .catch((error) => {
    console.error("Failed to send message:", error);
  });
```

### React Hook Example

```javascript
import { useState } from "react";

const useWhatsAppMessage = (baseUrl, authToken) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const sendMessage = async (projectId, chatId, messageData) => {
    setLoading(true);
    setError(null);

    try {
      const response = await fetch(
        `${baseUrl}/user/project/${projectId}/chat/${chatId}/messages`,
        {
          method: "POST",
          headers: {
            Authorization: `Bearer ${authToken}`,
            "Content-Type": "application/json",
          },
          body: JSON.stringify(messageData),
        },
      );

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.message || "Failed to send message");
      }

      return result.data;
    } catch (err) {
      setError(err.message);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  return { sendMessage, loading, error };
};

// Usage in React component
const MessageComponent = ({ projectId, chatId }) => {
  const { sendMessage, loading, error } = useWhatsAppMessage(
    BASE_URL,
    AUTH_TOKEN,
  );

  const handleSendText = async () => {
    try {
      const result = await sendMessage(projectId, chatId, {
        type: "text",
        text: {
          body: "Hello from React!",
          previewUrl: false,
        },
      });
      console.log("Message sent:", result);
    } catch (err) {
      console.error("Error:", err);
    }
  };

  return (
    <div>
      <button onClick={handleSendText} disabled={loading}>
        {loading ? "Sending..." : "Send Message"}
      </button>
      {error && <div className="error">{error}</div>}
    </div>
  );
};
```

## 📋 Best Practices

### 1. Error Handling

- Always check response status codes
- Implement retry logic for failed requests
- Show user-friendly error messages
- Log errors for debugging

### 2. Performance

- Use loading states in UI
- Implement message queuing for bulk sends
- Cache frequently used data
- Use connection pooling if making many requests

### 3. Security

- Store authentication tokens securely
- Never expose tokens in client-side code
- Use HTTPS for all API calls
- Validate all input data

### 4. User Experience

- Provide immediate feedback for message sends
- Show message status (sent, delivered, read)
- Allow users to retry failed messages
- Implement offline support if needed

### 5. Message Guidelines

- Respect WhatsApp messaging policies
- Use templates for notifications
- Keep messages concise and relevant
- Avoid spam and unsolicited messages

## 📝 Important Notes

- **WhatsApp Credentials**: Ensure your project has properly configured WhatsApp credentials in the backend
- **Media Files**: For media messages, use `id` for previously uploaded media or `link` for publicly accessible URLs
- **Rate Limits**: Be aware of WhatsApp's rate limiting policies
- **Message Templates**: Templates must be pre-approved in your WhatsApp Business account
- **Business Hours**: Consider time zones and business hours when sending messages

For more advanced features or questions about integration, don't hesitate to contact the backend team!
