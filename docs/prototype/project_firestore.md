# Struktur Firestore

## Overview

Struktur Firestore ini digunakan untuk menyimpan data project, chat, dan messages. Setiap collection memiliki subcollection yang terstruktur untuk mendukung fitur WhatsApp Business API integration.

## 📁 Collection `Projects`

Collection utama yang menyimpan data project WhatsApp Business.

### 📄 Document Structure (ProjectModel)

Setiap document dalam collection `Projects` memiliki struktur sebagai berikut:

```typescript
interface ProjectModel {
  uid: string; // Document ID auto-generated
  projectName: string; // Nama project (wajib)
  description: string; // Deskripsi project
  createdAt: string; // ISO string timestamp
  updatedAt: string; // ISO string timestamp
  whatsappCredentials: WhatsappCredentials; // Kredensial WhatsApp
  ownerUserUid: string; // UID user pemilik project
}
```

### 🔑 WhatsApp Credentials

```typescript
interface WhatsappCredentials {
  phoneNumber: string; // Nomor telepon WhatsApp Business
  provider: string; // Provider (biasanya "meta")
  bearerToken: string; // Token untuk WhatsApp API
  whatsappBusinessAccountId: string; // ID Business Account
  phoneNumberId: string; // ID Phone Number
  webhookVerificationToken: string; // Token untuk webhook verification
}
```

---

## 💬 Subcollection `chats`

Setiap document project memiliki subcollection `chats` yang menyimpan data percakapan.

### 📄 Document Structure (ChatModel)

```typescript
interface ChatModel {
  id: string; // Document ID auto-generated
  phoneNumber: string; // Nomor telepon kontak
  name: string; // Nama kontak
  label: string | null; // Label/kategori kontak
  createdAt: string; // ISO string timestamp
  updatedAt: string; // ISO string timestamp
  lastMessage: MessageModel<string> | null; // Pesan terakhir
}
```

### 📋 Field Details

- `id`: Unique identifier untuk chat (document ID)
- `phoneNumber`: Nomor telepon kontak dalam format internasional (+62xxx)
- `name`: Nama kontak yang diambil dari WhatsApp
- `label`: Opsional, untuk kategorisasi kontak
- `lastMessage`: Pesan terakhir dalam chat, null jika belum ada pesan

---

## 📨 Subcollection `messages`

Setiap document chat memiliki subcollection `messages` yang menyimpan riwayat percakapan.

### 📄 Document Structure (MessageModel)

```typescript
interface MessageModel<DATE = string> {
  message: Message<DATE>; // Data pesan utama
  statuses: Statuses<DATE> | null; // Status pengiriman
  direction: "in" | "out"; // Arah pesan
  createdAt: DATE; // Timestamp pembuatan
  sentBy?: {
    // Info pengirim (untuk pesan keluar)
    uid: string;
    name: string;
    email: string;
  };
}
```

### 🔤 Message Types

```typescript
type TMessageType =
  | "text" // Pesan teks
  | "image" // Pesan gambar
  | "audio" // Pesan audio
  | "video" // Pesan video
  | "document" // Pesan dokumen
  | "sticker" // Sticker
  | "location" // Lokasi
  | "contacts" // Kontak
  | "reaction" // Reaksi emoji
  | "interactive" // Pesan interaktif
  | "button" // Button reply
  | "order" // Pesanan
  | "system" // Pesan sistem
  | "unsupported"; // Tipe tidak didukung
```

### 📊 Status Tracking

```typescript
interface Statuses<DATE = string> {
  latest: "sent" | "delivered" | "read" | "failed"; // Status terakhir
  timestamp: DATE; // Timestamp status terakhir
  details: {
    sent: DATE | null; // Waktu terkirim
    delivered: DATE | null; // Waktu terkirim
    read: DATE | null; // Waktu dibaca
    failed: DATE | null; // Waktu gagal
  } | null;
}
```

### 📝 Message Content Examples

#### Text Message

```typescript
{
  text: {
    body: "Hello, how are you?";
  }
}
```

#### Media Message (Image/Audio/Video/Document)

```typescript
{
  image: {
    id: "media_id",
    mime_type: "image/jpeg",
    sha256: "hash_value",
    caption: "Optional caption"
  }
}
```

#### Location Message

```typescript
{
  location: {
    latitude: "-6.2088",
    longitude: "106.8456",
    name: "Jakarta",
    address: "Indonesia"
  }
}
```

#### Contact Message

```typescript
{
  contacts: [
    {
      name: {
        formatted_name: "John Doe",
        first_name: "John",
        last_name: "Doe",
      },
      phones: [
        {
          phone: "+6281234567890",
          type: "MOBILE",
        },
      ],
    },
  ];
}
```

#### Interactive Message

```typescript
{
  interactive: {
    type: "button_reply",
    button_reply: {
      id: "button_id",
      title: "Button Text"
    }
  }
}
```

### 🔄 Message Context

```typescript
interface MessageContext {
  forwarded: boolean; // Apakah pesan diforward
  frequently_forwarded: boolean; // Apakah sering diforward
  from: string | null; // Pengirim asli
  id: string | null; // Message ID asli
  referred_product?: Record<string, unknown>; // Product reference
}
```

---

## 🔗 TypeScript Interfaces Reference

Semua interface TypeScript tersedia di:

- `src/types/firestore/projectModel.ts` - ProjectModel dan WhatsappCredentials
- `src/types/firestore/chatModel.ts` - ChatModel dan struktur chat
- `src/types/firestore/messageModel.ts` - MessageModel dan tipe pesan

## 📅 Timestamp Handling

- Semua tanggal disimpan sebagai ISO string format di aplikasi
- Firestore otomatis mengkonversi ke Timestamp
- Konversi menggunakan `dayjs` untuk consistency
- Format: `YYYY-MM-DDTHH:mm:ss.sssZ`

## 🔒 Security Notes

- `bearerToken` dan `webhookVerificationToken` adalah sensitive data
- Pastikan untuk menerapkan proper security rules di Firestore
- Hindari mengekspos credentials di client-side code
