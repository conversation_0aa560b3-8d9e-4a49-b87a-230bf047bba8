# Theming System Documentation

## Overview

This document provides a comprehensive overview of the theming system in the Ideal Public React application. The theming system is built using Tailwind CSS v4, HeroUI components, and Redux state management, providing a consistent and flexible approach to styling across the application.

## Styling Libraries and Frameworks

### Tailwind CSS v4

- Version: 4.1.11
- Used as the primary utility-first CSS framework
- Provides a comprehensive set of utility classes for styling
- Configured with the `dark` class strategy for dark mode implementation

### HeroUI

- Modern React component library built on Tailwind CSS and React Aria
- Provides pre-built accessible components with consistent styling
- Includes components like buttons, cards, switches, and navigation elements
- Offers built-in dark mode support and theme customization
- Version: @heroui/react ^2.8.4 and @heroui/theme 2.4.22

### Tailwind Variants

- Library that brings the concept of variants and slots to Tailwind CSS
- Enables better component styling and design systems
- Allows for creating reusable component variations with consistent APIs
- Version: 3.1.1

## Dark Mode and Light Mode Implementation

### Architecture

The dark/light mode system follows a Redux-based approach with the following components:

1. **Redux State Management**:

   - Theme state stored in `uiSlice` with `theme: "light" | "dark"` property
   - Actions: `toggleTheme()`, `setTheme(theme)`
   - Selector: `useAppSelector((state) => state.ui.theme)`

2. **Theme Observer Component**:

   - Located at `src/components/theme/ThemeObserver.tsx`
   - Handles theme initialization and persistence
   - Follows priority: localStorage → system preference → default ("light")
   - Applies theme to DOM by adding/removing "dark" class on `document.documentElement`
   - Persists theme to localStorage

3. **Theme Toggle Component**:
   - Located at `src/components/theme/DarkModeToggle.tsx`
   - Provides UI for users to toggle between themes
   - Uses HeroUI Switch component with Sun/Moon icons
   - Dispatches `toggleTheme()` action on user interaction

### Implementation Rules

- Use the `dark:` prefix for dark mode variants (e.g., `dark:bg-gray-800`)
- The "dark" class is applied to the root HTML element
- Theme preference is persisted in localStorage under the "theme" key
- System preference is respected when no explicit theme is saved

### Usage Guidelines

```jsx
// Example of proper dark mode implementation
<div className="bg-white dark:bg-gray-800 text-gray-900 dark:text-white">
  Content that adapts to theme
</div>
```

## Color Palette Specifications

### Primary Colors

- Light mode primary: `#3B82F6` (blue-500)
- Light mode primary hover: `#2563EB` (blue-600)
- Light mode primary disabled: `#93C5FD` (blue-300)
- Dark mode primary: `#60A5FA` (blue-400)
- Dark mode primary hover: `#3B82F6` (blue-500)
- Dark mode primary disabled: `#1E3A8A` (blue-900)

### Custom Properties

Defined in `src/styles/globals.css`:

```css
:root {
  /* Primary color scheme */
  --color-primary: #3b82f6; /* blue-500 */
  --color-primary-hover: #2563eb; /* blue-600 */
  --color-primary-disabled: #93c5fd; /* blue-300 */
}

html {
  color-scheme: light;
}

html.dark {
  color-scheme: dark;
  /* Dark mode primary color variants */
  --color-primary: #60a5fa; /* blue-400 */
  --color-primary-hover: #3b82f6; /* blue-500 */
  --color-primary-disabled: #1e3a8a; /* blue-900 */
}
```

### Color Usage Guidelines

- Use Tailwind's built-in color classes for standard colors (e.g., `bg-blue-500`, `text-gray-900`)
- Use custom CSS variables for primary color variants that change with theme
- Maintain sufficient contrast ratios for accessibility (minimum 4.5:1 for normal text, 3:1 for large text)
- Use semantic color names when possible (e.g., `text-primary`, `bg-secondary`)

## Typography Rules and Font Specifications

### Default Typography

- Typography is handled through Tailwind CSS defaults and HeroUI components
- No custom font configuration is implemented
- Font sizes follow Tailwind's scale (text-xs, text-sm, text-base, text-lg, text-xl, etc.)
- Line heights follow Tailwind's scale (leading-4, leading-5, leading-6, etc.)

### Typography Classes

- Use Tailwind's responsive font size classes for different screen sizes
- Apply appropriate line heights for readability
- Use HeroUI's typography components when available for consistency

### Best Practices

- Maintain consistent typography hierarchy throughout the application
- Use appropriate font weights for emphasis (font-normal, font-medium, font-semibold, font-bold)
- Ensure proper spacing with margin and padding utilities
- Consider responsive typography for different screen sizes

## Component Styling Patterns and Conventions

### HeroUI Components

- Use HeroUI components for standard UI elements (buttons, cards, inputs, etc.)
- Follow HeroUI's prop-based theming approach
- Leverage HeroUI's built-in dark mode support

### Tailwind Variants for Custom Components

- Use `tv` function to create reusable component variants
- Define slots for complex components with multiple parts
- Use compound variants for complex styling combinations
- Set default variants for consistent base styling

Example:

```jsx
import { tv } from "tailwind-variants";

const button = tv({
  base: "font-medium bg-blue-500 text-white rounded-full active:opacity-80",
  variants: {
    color: {
      primary: "bg-blue-500 text-white",
      secondary: "bg-purple-500 text-white",
    },
    size: {
      sm: "text-sm",
      md: "text-base",
      lg: "px-4 py-3 text-lg",
    },
  },
  compoundVariants: [
    {
      size: ["sm", "md"],
      class: "px-3 py-1",
    },
  ],
  defaultVariants: {
    size: "md",
    color: "primary",
  },
});
```

### Styling Conventions

- Use functional React components with TypeScript
- Follow the import order: React → third-party → blank line → `@/` aliases
- Use PascalCase for component names
- Use camelCase for utility functions
- Prefer direct Tailwind classes over custom CSS for most component styling
- Use Tailwind utility classes directly in JSX for simple styling
- Consider using `tailwind-variants` for complex components with multiple variants

## CSS-in-JS and Utility Class Usage Patterns

### Tailwind Utility Classes

- Use Tailwind's utility classes directly in className props
- Leverage responsive prefixes (sm:, md:, lg:, xl:, 2xl:)
- Use state variants (hover:, focus:, active:, etc.)
- Implement dark mode variants (dark:)

### Responsive Design

- Use Tailwind's responsive breakpoints consistently
- Mobile-first approach with `sm`, `md`, `lg`, `xl`, `2xl` prefixes
- Consider touch targets for mobile devices (minimum 44px)

### State-Based Styling

- Use Tailwind's state variants for interactive elements
- Implement focus styles for accessibility
- Use hover and active states for better UX
- Consider reduced motion preferences with `motion-reduce:` variants

## Theme Configuration Files and Their Purposes

### Tailwind Configuration

- **File**: `tailwind.config.js`
- **Purpose**: Main configuration for Tailwind CSS and HeroUI plugin
- **Key settings**:
  - `darkMode: "class"` - Uses class strategy for dark mode
  - Content paths for scanning components
  - HeroUI plugin integration
  - Theme extensions (if any)

### Global Styles

- **File**: `src/styles/globals.css`
- **Purpose**: Global CSS custom properties and base styles
- **Key features**:
  - CSS custom properties for theming
  - Dark mode specific overrides
  - Custom scrollbar styles
  - Color-scheme declarations

### Redux Theme State

- **File**: `src/store/uiSlice.ts`
- **Purpose**: Manages theme state in Redux store
- **Key features**:
  - Theme property with "light" | "dark" values
  - toggleTheme and setTheme actions
  - Integration with ThemeObserver component

### Theme Provider

- **File**: `src/components/theme/ThemeObserver.tsx`
- **Purpose**: Manages theme application and persistence
- **Key features**:
  - DOM class manipulation
  - localStorage persistence
  - System preference detection
  - Theme initialization logic

## Code Examples

### Basic Component with Theming

```jsx
// Example of a properly themed component
import { Card, CardHeader, CardBody, CardFooter } from "@heroui/react";

const ThemedCard = ({ title, children, actions }) => {
  return (
    <Card className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700">
      <CardHeader className="text-gray-900 dark:text-white">
        <h3>{title}</h3>
      </CardHeader>
      <CardBody className="text-gray-700 dark:text-gray-300">
        {children}
      </CardBody>
      <CardFooter>{actions}</CardFooter>
    </Card>
  );
};
```

### Component with Tailwind Variants

```jsx
// Example using tailwind-variants for complex styling
import { tv } from "tailwind-variants";

const panel = tv({
  base: "rounded-lg border",
  variants: {
    variant: {
      default: "bg-white border-gray-200 dark:bg-gray-800 dark:border-gray-700",
      elevated:
        "bg-gray-50 border-gray-300 dark:bg-gray-700 dark:border-gray-600",
    },
    padding: {
      compact: "p-3",
      regular: "p-6",
      spacious: "p-8",
    },
  },
  defaultVariants: {
    variant: "default",
    padding: "regular",
  },
});

const Panel = ({ variant, padding, children }) => {
  return <div className={panel({ variant, padding })}>{children}</div>;
};
```

### Dark Mode Toggle Component

```jsx
// Example of theme toggle implementation
import React from "react";
import { Switch } from "@heroui/switch";
import { Sun, Moon } from "lucide-react";

import { useAppSelector, useAppDispatch } from "@/store/hooks";
import { toggleTheme } from "@/store/uiSlice";

const DarkModeToggle: React.FC = () => {
  const theme = useAppSelector((state) => state.ui.theme);
  const dispatch = useAppDispatch();

  const handleToggle = () => {
    dispatch(toggleTheme());
  };

  return (
    <div className="flex items-center space-x-2 px-2 py-1">
      <Sun className="h-4 w-4 text-yellow-500" />
      <Switch
        aria-label="Toggle dark mode"
        isSelected={theme === "dark"}
        onChange={handleToggle}
      />
      <Moon className="h-4 w-4 text-blue-500" />
    </div>
  );
};

export default DarkModeToggle;
```

### Message Bubble Component with Theme Awareness

```jsx
// Example from src/components/chat/panels/MessageBubble.tsx
const MessageBubble: React.FC<MessageBubbleProps> = memo(
  ({ message, isFirstInGroup, isLastInGroup, activeChat }) => {
    const isOutgoing = message.direction === "out";
    const isOwnMessage = isOutgoing;

    return (
      <div
        className={`flex ${isOwnMessage ? "justify-end" : "justify-start"} ${isLastInGroup ? "mb-4" : "mb-1"}`}
      >
        <div
          className={`flex flex-col ${isOwnMessage ? "items-end" : "items-start"} max-w-xs lg:max-w-md xl:max-w-lg relative`}
        >
          <div
            className={`
              px-4 py-2 shadow-sm max-w-full
              ${
                isOwnMessage
                  ? "bg-blue-500 text-white"
                  : "bg-white dark:bg-gray-800 dark:text-white border border-gray-200 dark:border-gray-700"
              }
              ${
                isFirstInGroup && isLastInGroup
                  ? "rounded-2xl"
                  : isFirstInGroup
                    ? isOwnMessage
                      ? "rounded-2xl rounded-br-md"
                      : "rounded-2xl rounded-bl-md"
                    : isLastInGroup
                      ? isOwnMessage
                        ? "rounded-2xl rounded-tr-md"
                        : "rounded-2xl rounded-tl-md"
                      : isOwnMessage
                        ? "rounded-r-2xl rounded-l-md"
                        : "rounded-l-2xl rounded-r-md"
              }
            `}
          >
            {/* Message content */}
            <div className="text-sm">{renderMessageContent()}</div>
          </div>
        </div>
      </div>
    );
  },
);
```

### Message Bubble Dropdown with Theme Awareness

```jsx
// Example from src/components/chat/panels/MessageBubbleDropdown.tsx
const MessageBubbleDropdown: React.FC<MessageBubbleDropdownProps> = memo(
  ({ className = "", onAction }) => {
    return (
      <div className={className}>
        <Dropdown>
          <DropdownTrigger>
            <Button
              isIconOnly
              className={`w-6 h-6 min-w-0 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200`}
              size="sm"
              variant="light"
            >
              <IoEllipsisVertical className="w-4 h-4" />
            </Button>
          </DropdownTrigger>
          <DropdownMenu aria-label="Message actions" onAction={handleAction}>
            <DropdownItem
              key="copyMessageId"
              className="text-gray-700 dark:text-gray-300"
            >
              Copy Message ID
            </DropdownItem>
          </DropdownMenu>
        </Dropdown>
      </div>
    );
  },
);
```

### Default Layout with Theme Awareness

```jsx
// Example from src/layouts/default.tsx
export default function DefaultLayout({ children }: DefaultLayoutProps) {
  const { project, projectLoadStatus } = useOutletContext<{
    project?: Project;
    projectLoadStatus: "none" | "loading" | "error" | "success";
  }>();
  const dispatch = useAppDispatch();
  const { visiblePanels, screenSize } = useAppSelector((state) => state.ui);

  return (
    <div className="relative h-screen bg-gray-50 dark:bg-gray-900 overflow-hidden">
      {/* Main Menu (Vertical Sidebar) - Always positioned absolutely to prevent layout shifts */}
      {visiblePanels.mainMenu && project && <MainMenu project={project} />}

      {/* Content Area - Children passed from parent component */}
      {projectLoadStatus === "loading" && (
        <div className="flex h-full items-center justify-center">
          <Spinner size="lg" />
        </div>
      )}
      {projectLoadStatus === "success" && children}
    </div>
  );
}
```

## Best Practices for Maintaining Consistency

### Naming Conventions

- Use PascalCase for React components
- Use camelCase for hooks and utility functions
- Use SCREAMING_SNAKE_CASE for constants
- Use descriptive names for utility classes and variants

### Component Organization

- Store reusable components in `src/components/<Feature>/index.ts`
- Use feature-based organization in `src/pages/<feature>/`
- Keep related components together in directories
- Co-locate tests as `*.test.tsx` beside the implementation

### Styling Consistency

- Always use the `@/` alias for imports from src
- Prefer HeroUI components for standard UI elements
- Use direct Tailwind classes for simple styling
- Consider using Tailwind variants for complex components with multiple variants
- Maintain consistent spacing with Tailwind's scale
- Follow the same dark mode implementation pattern throughout the app

### Performance Considerations

- Use CSS custom properties for frequently changing theme values
- Leverage Tailwind's JIT compiler for optimal build performance
- Avoid unnecessary re-renders by properly structuring component state
- Use memoization when appropriate for expensive computations

### Accessibility

- Ensure sufficient color contrast ratios
- Implement proper focus management
- Use semantic HTML elements
- Support reduced motion preferences
- Test with screen readers

### Testing

- Test components in both light and dark modes
- Verify theme persistence across page reloads
- Test system preference detection
- Ensure all interactive elements have proper focus states
