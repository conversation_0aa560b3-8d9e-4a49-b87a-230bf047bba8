# Ideal Public

Ideal Public is a comprehensive React + Vite workspace for managing multi-project WhatsApp operations. It features Firebase authentication, a modern HeroUI/Tailwind interface, and Redux-driven state management to enable teams to efficiently explore dashboard metrics, manage contacts, and triage conversations from a unified platform.

## Features

- **Project-aware routing** with protected authentication flows for login, signup, project selection, and account settings
- **Chat workspace** with mock fixtures that hydrate Redux Toolkit slices for conversations, messages, and UI state
- **Dashboard** with cards, quick actions, and statistics summaries implemented with HeroUI components and Tailwind 4 utility classes
- **Responsive layout** that adapts sidebar visibility by screen size and persists user theme preferences
- **Dark mode support** with theme persistence across sessions

## Tech Stack

### Core Technologies

- **React 18** - UI library with modern hooks and features
- **Vite** - Fast build tool and development server
- **TypeScript** - Type-safe JavaScript development
- **Redux Toolkit** - State management with Redux

### UI & Styling

- **HeroUI 2** - Modern React component library
- **Tailwind CSS 4** - Utility-first CSS framework
- **Framer Motion** - Animation library
- **Lucide React** - Icon library
- **Tailwind Variants** - Variant-based styling

### Development & Data

- **React Router** - Client-side routing
- **Firebase** - Authentication and Firestore database
- **Axios** - HTTP client for API requests
- **Day.js** - Date manipulation library

### Development Tools

- **ESLint** - Code linting with React and TypeScript support
- **Prettier** - Code formatting
- **Vitest** - Testing framework (planned implementation)

## Project Structure

```
src/
  components/        # Shared UI components
    auth/            # Authentication-related components
    chat/            # Chat workspace components
      panels/        # Chat panel components
    providers/       # React context providers
    route/           # Protected route components
    theme/           # Theme-related components
  layouts/           # Reusable application layouts
  pages/             # Feature-based page components
    dashboard/       # Dashboard feature
    chats/           # Chat feature
    contacts/        # Contact management
    settings/        # User and project settings
    login/           # Authentication
    signup/          # Registration
    select-project/  # Project selection
  store/             # Redux state management
    slices/          # Feature state slices
    hooks.ts         # Typed Redux hooks
    index.ts         # Store configuration
  services/          # API and external service integrations
    firebase/        # Firebase services
    main/            # Main API services
  data/              # Mock data and fixtures
  types/             # TypeScript type definitions
    auth/            # Authentication types
    firestore/       # Firestore model types
  utils/             # Utility functions
  styles/            # Global styles and theme configurations
  main.tsx           # Application entry point
  provider.tsx       # App-level providers
  App.tsx            # Root component with routing
public/              # Static assets
docs/                # Project documentation
```

## Installation & Setup

### Prerequisites

- Node.js 18+
- npm or yarn

### Steps

1. **Clone the repository**

   ```bash
   git clone <repository-url>
   cd ideal-public
   ```

2. **Install dependencies**

   ```bash
   npm install
   ```

3. **Set up environment variables**
   Create a `.env.local` file in the root directory and add your Firebase configuration:

   ```env
   VITE_FIREBASE_API_KEY=your_api_key
   VITE_FIREBASE_AUTH_DOMAIN=your_auth_domain
   VITE_FIREBASE_PROJECT_ID=your_project_id
   VITE_FIREBASE_STORAGE_BUCKET=your_storage_bucket
   VITE_FIREBASE_MESSAGING_SENDER_ID=your_messaging_sender_id
   VITE_FIREBASE_APP_ID=your_app_id
   ```

4. **Start the development server**

   ```bash
   npm run dev
   ```

5. **Open your browser** to the URL displayed in the terminal (typically `http://localhost:5173`)

## Development Workflow

### Available Scripts

- `npm run dev` - Start the Vite development server with HMR
- `npm run build` - Run TypeScript type checks and create the production bundle
- `npm run preview` - Serve the built bundle locally for testing
- `npm run lint` - Run ESLint with auto-fix (Prettier integrated)
- `npm run format` - Apply Prettier formatting across the repository

### Testing

Testing with Vitest is planned but not yet configured. When implemented:

- `npx vitest` - Run unit and component test suites
- `npx vitest --run` - Run tests in CI mode
- `npx vitest --ui` - Run tests with interactive UI for debugging

## Project Architecture

### Entry Point Flow

The application follows a clear entry chain:

1. [`src/main.tsx`](src/main.tsx) - Application bootstrap
2. [`src/provider.tsx`](src/provider.tsx) - Global providers setup
3. [`src/App.tsx`](src/App.tsx) - Root component with routing configuration

### State Management

- Redux Toolkit is used for state management with slices organized by feature
- Typed hooks are provided in [`src/store/hooks.ts`](src/store/hooks.ts)
- Mock data is provided in [`src/data/`](src/data/) for development

### Component Organization

- Feature screens live in [`src/pages/<feature>/`](src/pages/)
- Reusable components are organized in [`src/components/<Feature>/`](src/components/)
- Layouts are stored in [`src/layouts/`](src/layouts/)

### Routing

- Protected routes are implemented in [`src/components/route/`](src/components/route/)
- Route guards ensure proper authentication and project selection

## Coding Standards

### Style Guidelines

- **TypeScript strict mode** is enabled for type safety
- **Functional React components** and typed hooks are preferred
- **Import order**: React → third-party → blank line → `@/` aliases
- **Naming conventions**:
  - PascalCase for components
  - camelCase for hooks and utilities
  - SCREAMING_SNAKE_CASE for constants
- **Styling**: Tailwind v4 utilities and HeroUI props are preferred
- **Formatting**: 2-space indentation with semicolons, enforced by Prettier

### Linting Rules

- ESLint configuration is defined in [`eslint.config.mjs`](eslint.config.mjs)
- Rules enforce React best practices, hooks rules, and unused import detection
- Prettier integration ensures consistent code formatting

## Testing Guidelines

### Testing Framework

- **Vitest** with Testing Library assertions for unit and component tests
- Mock Redux and router context via helpers in [`src/store/`](src/store/) and [`src/provider.tsx`](src/provider.tsx)

### Test Organization

- Co-locate tests as `*.test.tsx` beside implementation files
- Mirror filenames (e.g., `FeaturePanel.test.tsx`)
- Keep test specs focused on observable behavior
- Store cross-feature mocks in [`src/data/`](src/data/)

### Testing Best Practices

- Mock Firebase and REST clients with fixtures from [`src/data/`](src/data/)
- Prefer dependency injection over jest.mock globals
- Validate async flows remain serializable for Redux middleware
- Cover guarded routes in [`src/components/route/`](src/components/route/)

## Environment Configuration

### Environment Variables

- Secrets must be stored in `.env.local` with `VITE_` prefixes
- Never commit Firebase keys or project IDs to version control
- Example environment variables:
  ```env
  VITE_FIREBASE_API_KEY=your_api_key
  VITE_FIREBASE_AUTH_DOMAIN=your_auth_domain
  VITE_FIREBASE_PROJECT_ID=your_project_id
  VITE_FIREBASE_STORAGE_BUCKET=your_storage_bucket
  VITE_FIREBASE_MESSAGING_SENDER_ID=your_messaging_sender_id
  VITE_FIREBASE_APP_ID=your_app_id
  ```

### Service Integration

- New services should be added to [`src/services/`](src/services/)
- Reuse [`baseServiceEndpoint.ts`](src/services/main/baseServiceEndpoint.ts) for API services
- Document added environment variables in pull request descriptions

## Contribution Guidelines

### Commit Standards

- Follow Conventional Commits format:
  - `feat(chat): add message search`
  - `fix(auth): resolve login validation issue`
  - `docs(readme): update installation instructions`
- Keep commit scope tight and focused

### Pull Request Process

- PRs should explain intent and list key changes
- Reference related issues in the description
- Attach UI screenshots when visual changes are made
- Confirm lint, format, build, and relevant tests succeed
- Note any skipped steps and mitigation strategies
- Request reviewer sign-off for updates touching:
  - Routing guards
  - State slices
  - Shared types

### Code Review

- All code changes must be reviewed before merging
- Ensure adherence to coding standards and project architecture
- Verify that documentation is updated as needed

## Deployment

### Build Process

1. Run `npm run build` to create the production bundle
2. Inspect the output in `dist/` before deploying
3. Ensure environment variables are properly configured on the hosting provider

### Hosting Considerations

- A `vercel.json` SPA rewrite is included for Vercel hosting
- Firebase credentials must be stored securely as environment variables
- Ensure proper routing configuration for client-side routing

## Documentation

- [`AGENTS.md`](AGENTS.md) - Detailed contributor playbook for agents and maintainers
- [`docs/`](docs/) - Additional project documentation
  - [`docs/dark-mode.md`](docs/dark-mode.md) - Dark mode implementation details
  - [`docs/prototype/`](docs/prototype/) - Prototype documentation

## License

This project is licensed under the [MIT License](./LICENSE).
