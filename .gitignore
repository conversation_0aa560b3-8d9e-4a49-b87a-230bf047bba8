# Dependencies
node_modules/
.npm
.yarn
.pnpm-store

# Package manager lock files
package-lock.json
yarn.lock
pnpm-lock.yaml
bun.lockb

# Build outputs
dist/
build/
dist-ssr/
*.tsbuildinfo

# Vite specific
.vite/
*.env.*

# Testing
coverage/
.nyc_output/
.vitest-cache/
junit.xml
test-results/

# Environment files
.env
.env.local
.env.development
.env.development.local
.env.test
.env.test.local
.env.production
.env.production.local

# Local files
*.local

# IDE and editor files
!.vscode/extensions.json
!.vscode/settings.json
.vscode/*
.idea/
.qoder/
.figma/
.emacs
.spacemacs
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# OS generated files
.DS_Store
Thumbs.db
*.dll
*.so
*.dylib

# Temporary files
*.tmp
*.temp
.cache/
.temp/
*.cache

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*
*.out
*.err

# Database files
*.db
*.sqlite
*.sqlite3

# Firebase specific
.firebase/

# Deployment
.serverless/
.netlify/
.vercel/
*.zip
*.tar.gz

# Framework specific
.svelte-kit/
.next/
.nuxt/
.turbo/

# Runtime
pids/
*.pid
*.seed
*.pid.lock
