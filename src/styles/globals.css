@import "tailwindcss";

@config "../../tailwind.config.js";

:root {
  /* Primary color scheme */
  --color-primary: #3b82f6; /* blue-500 */
  --color-primary-hover: #2563eb; /* blue-600 */
  --color-primary-disabled: #93c5fd; /* blue-300 */
}

html {
  color-scheme: light;
}

html.dark {
  color-scheme: dark;
  /* Dark mode primary color variants */
  --color-primary: #60a5fa; /* blue-400 */
  --color-primary-hover: #3b82f6; /* blue-500 */
  --color-primary-disabled: #1e3a8a; /* blue-900 */
}

/* Custom scrollbar styles */
.transparent-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: transparent transparent;
}

.transparent-scrollbar:hover {
  scrollbar-color: rgba(0, 0, 0, 0.3) transparent;
}

.transparent-scrollbar::-webkit-scrollbar {
  width: 8px;
}

.transparent-scrollbar::-webkit-scrollbar-track {
  background: transparent;
}

.transparent-scrollbar::-webkit-scrollbar-thumb {
  background: transparent;
  border-radius: 4px;
  transition: background 0.3s ease;
}

.transparent-scrollbar:hover::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.3);
}
