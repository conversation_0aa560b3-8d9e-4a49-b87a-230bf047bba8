import dayjs from "dayjs";
import { Timestamp } from "firebase/firestore";

/**
 * Convert a date value to a Firestore Timestamp
 * @param date - A string, Date object, or Timestamp
 * @returns A Firestore Timestamp
 */
export const toTimestamp = (date: string | Date | Timestamp): Timestamp => {
  if (date instanceof Timestamp) {
    return date;
  } else if (date instanceof Date) {
    return Timestamp.fromDate(date);
  } else {
    return Timestamp.fromDate(dayjs(date).toDate());
  }
};
