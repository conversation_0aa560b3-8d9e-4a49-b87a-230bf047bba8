import dayjs from "dayjs";

/**
 * Format date to a readable string
 * @param date - Date string or Date object
 * @returns Formatted date string
 */
export const formatDate = (date: string | Date): string => {
  if (!date) return "";

  return dayjs(date).format("MMM D, YYYY h:mm A");
};

/**
 * Format date to a short readable string
 * @param date - Date string or Date object
 * @returns Formatted date string
 */
export const formatShortDate = (date: string | Date): string => {
  if (!date) return "";

  return dayjs(date).format("MMM D, YYYY");
};
