import { Project } from "@/services/main/projectMainService.ts";

// Re-export Project type for convenience
export type { Project };

export interface ProjectListProps {
  projects: Project[];
  viewMode: "grid" | "list";
  onProjectSelect: (project: Project) => void;
}

export interface ProjectCardProps {
  project: Project;
  onProjectSelect: (project: Project) => void;
}

export interface SearchBarProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
}
