// WhatsApp Webhook Root Structure
export interface WhatsAppWebhook {
  object: "whatsapp_business_account";
  entry: WhatsAppEntry[];
}

export interface WhatsAppEntry {
  id: string; // WHATSAPP_BUSINESS_ACCOUNT_ID
  changes: WhatsAppChange[];
}

export interface WhatsAppChange {
  value: WhatsAppChangeValue;
  field: "messages";
}

export interface WhatsAppChangeValue {
  messaging_product: "whatsapp";
  metadata: WhatsAppMetadata;
  contacts?: WhatsAppContact[];
  messages?: WhatsAppMessage[];
  statuses?: WhatsAppStatus[];
  errors?: WhatsAppError[];
}

export interface WhatsAppMetadata {
  display_phone_number: string;
  phone_number_id: string;
}

// Contact Information
export interface WhatsAppContact {
  profile: {
    name: string;
  };
  wa_id: string; // WhatsApp ID
  user_id?: string; // Additional unique identifier
}

// Core Message Structure
export interface WhatsAppMessage {
  from: string; // Sender's phone number
  id: string; // Message ID (wamid.xxx)
  timestamp: string; // Unix timestamp
  type: WhatsAppMessageType;
  context?: WhatsAppMessageContext;

  // Message content based on type
  text?: WhatsAppTextMessage;
  image?: WhatsAppMediaMessage;
  audio?: WhatsAppMediaMessage;
  video?: WhatsAppMediaMessage;
  document?: WhatsAppDocumentMessage;
  sticker?: WhatsAppMediaMessage;
  location?: WhatsAppLocationMessage;
  contacts?: WhatsAppContactMessage[];
  button?: WhatsAppButtonMessage;
  interactive?: WhatsAppInteractiveMessage;
  reaction?: WhatsAppReactionMessage;
  referral?: WhatsAppReferralMessage;
  errors?: WhatsAppError[];
}

export type WhatsAppMessageType =
  | "text"
  | "image"
  | "audio"
  | "video"
  | "document"
  | "sticker"
  | "location"
  | "contacts"
  | "button"
  | "interactive"
  | "reaction"
  | "unknown";

// Message Context (for replies)
export interface WhatsAppMessageContext {
  forwarded?: boolean;
  frequently_forwarded?: boolean;
  from?: string; // WhatsApp ID of original sender
  id?: string; // Message ID being replied to
  referred_product?: {
    catalog_id: string;
    product_retailer_id: string;
  };
}

// Text Message
export interface WhatsAppTextMessage {
  body: string;
}

// Media Messages (Image, Audio, Video, Sticker)
export interface WhatsAppMediaMessage {
  id: string; // Media ID for retrieval
  mime_type: string;
  sha256: string;
  caption?: string; // For image/video
  filename?: string; // For documents
}

// Document Message
export interface WhatsAppDocumentMessage extends WhatsAppMediaMessage {
  filename: string;
}

// Location Message
export interface WhatsAppLocationMessage {
  latitude: number;
  longitude: number;
  name?: string;
  address?: string;
}

// Contact Message
export interface WhatsAppContactMessage {
  addresses?: {
    city?: string;
    country?: string;
    country_code?: string;
    state?: string;
    street?: string;
    type?: "HOME" | "WORK";
    zip?: string;
  }[];
  birthday?: string;
  emails?: {
    email: string;
    type?: "HOME" | "WORK";
  }[];
  name: {
    formatted_name: string;
    first_name?: string;
    last_name?: string;
    middle_name?: string;
    suffix?: string;
    prefix?: string;
  };
  org?: {
    company?: string;
    department?: string;
    title?: string;
  };
  phones?: {
    phone: string;
    wa_id?: string;
    type?: "HOME" | "WORK";
  }[];
  urls?: {
    url: string;
    type?: "HOME" | "WORK";
  }[];
}

// Button Message (Quick Reply)
export interface WhatsAppButtonMessage {
  text: string;
  payload: string;
}

// Interactive Message (List Reply, Button Reply)
export interface WhatsAppInteractiveMessage {
  type: "list_reply" | "button_reply";
  list_reply?: {
    id: string;
    title: string;
    description?: string;
  };
  button_reply?: {
    id: string;
    title: string;
  };
}

// Reaction Message
export interface WhatsAppReactionMessage {
  message_id: string; // ID of message being reacted to
  emoji: string;
}

// Referral Message (from Ads)
export interface WhatsAppReferralMessage {
  source_url: string;
  source_id: string;
  source_type: "ad" | "post";
  headline: string;
  body: string;
  media_type: "image" | "video";
  image_url?: string;
  video_url?: string;
  thumbnail_url?: string;
  ctwa_clid: string;
}

// Message Status Updates
export interface WhatsAppStatus {
  id: string; // Message ID
  status: "sent" | "delivered" | "read" | "failed";
  timestamp: string;
  recipient_id: string;
  conversation?: {
    id: string;
    expiration_timestamp?: string;
    origin: {
      type: "marketing" | "utility" | "authentication" | "service";
    };
  };
  pricing?: {
    billable: boolean;
    pricing_model: "CBP" | "NBP";
    category: "marketing" | "utility" | "authentication" | "service";
  };
  errors?: WhatsAppError[];
}

// Error Structure
export interface WhatsAppError {
  code: number;
  title: string;
  message?: string;
  error_data?: {
    details: string;
  };
}
