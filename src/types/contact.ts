export interface Contact {
  id: string;
  name: string;
  email: string;
  phone: string;
  address?: string;
  notes?: string;
  avatar?: string;
  createdAt: string;
  updatedAt: string;
}

export interface ContactFormData {
  name: string;
  email: string;
  phone: string;
  address: string;
  notes: string;
  avatar: File | null;
}

export interface ContactsState {
  contacts: Contact[];
  filteredContacts: Contact[];
  searchQuery: string;
  selectedContact: Contact | null;
  isModalOpen: boolean;
  modalMode: "add" | "edit" | "view";
  loading: boolean;
}

export interface ContactCardProps {
  contact: Contact;
  onEdit: (contact: Contact) => void;
  onDelete: (contactId: string) => void;
  onCall?: (phone: string) => void;
  onMessage?: (contact: Contact) => void;
}

export interface ContactModalProps {
  isOpen: boolean;
  mode: "add" | "edit" | "view";
  contact?: Contact;
  onClose: () => void;
  onSave: (contactData: ContactFormData) => void;
  onDelete?: (contactId: string) => void;
}

export interface SearchBarProps {
  value: string;
  onChange: (query: string) => void;
  placeholder?: string;
}

export interface ContactListProps {
  contacts: Contact[];
  viewMode: "grid" | "list";
  onContactSelect: (contact: Contact) => void;
}
