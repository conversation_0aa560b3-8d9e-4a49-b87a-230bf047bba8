import dayjs from "dayjs";
import {
  FirestoreDataConverter,
  QueryDocumentSnapshot,
  Timestamp,
} from "firebase/firestore";

import { toTimestamp } from "@/utils/firestoreUtils";

/**
 * Interface for application data (with `id` as document ID)
 */
export interface LabelModel {
  id: string;
  name: string;
  description: string;
  createdAt: string;
  updatedAt: string;
}

/**
 * Interface for raw Firestore data (without `id`)
 */
export interface RawLabelModel<DATE = Timestamp> {
  name: string;
  description: string;
  createdAt: DATE;
  updatedAt: DATE;
}

/**
 * Firestore converter for Label documents
 * Handles transformation between app data and Firestore data
 */
export class LabelModelFirestore {
  static converter(): FirestoreDataConverter<
    LabelModel,
    RawLabelModel<Timestamp>
  > {
    return {
      toFirestore(model: LabelModel): RawLabelModel<Timestamp> {
        // Note: We don't include the id in the Firestore document as it's the document ID
        return {
          name: model.name,
          description: model.description,
          createdAt: toTimestamp(model.createdAt),
          updatedAt: toTimestamp(model.updatedAt),
        };
      },

      fromFirestore(snapshot: QueryDocumentSnapshot): LabelModel {
        const data = snapshot.data() as RawLabelModel<Timestamp>;

        return {
          id: snapshot.id,
          name: data.name,
          description: data.description,
          createdAt: dayjs(data.createdAt.toDate()).toISOString(),
          updatedAt: dayjs(data.updatedAt.toDate()).toISOString(),
        };
      },
    };
  }
}
