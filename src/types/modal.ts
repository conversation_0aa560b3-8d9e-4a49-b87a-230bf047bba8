export type ModalType = "confirm" | "error" | "success" | "info";

export interface BaseModalConfig {
  title: string;
  message: string;
  onClose?: () => void;
  autoCloseTimer?: number; // in milliseconds
}

export interface ConfirmModalConfig extends BaseModalConfig {
  onConfirm?: () => void;
  onCancel?: () => void;
  confirmText?: string;
  cancelText?: string;
}

export interface ErrorModalConfig extends BaseModalConfig {
  onConfirm?: () => void;
  confirmText?: string;
}

export interface SuccessModalConfig extends BaseModalConfig {
  onConfirm?: () => void;
  confirmText?: string;
}

export interface InfoModalConfig extends BaseModalConfig {
  onConfirm?: () => void;
  confirmText?: string;
}

export type ModalConfig =
  | ConfirmModalConfig
  | ErrorModalConfig
  | SuccessModalConfig
  | InfoModalConfig;

export interface ModalState {
  type: ModalType | null;
  config: ModalConfig | null;
  isOpen: boolean;
}

export interface ModalContextType {
  state: ModalState;
  showConfirm: (config: ConfirmModalConfig) => void;
  showError: (config: ErrorModalConfig) => void;
  showSuccess: (config: SuccessModalConfig) => void;
  showInfo: (config: InfoModalConfig) => void;
  closeModal: () => void;
}
