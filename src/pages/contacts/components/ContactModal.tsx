import React, { useState, useEffect } from "react";
import dayjs from "dayjs";
import { useDispatch } from "react-redux";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>dal<PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Button,
  Input,
  Textarea,
  Avatar,
  Divider,
} from "@heroui/react";
import {
  User,
  Mail,
  Phone,
  MapPin,
  FileText,
  Upload,
  Edit,
  Trash2,
} from "lucide-react";

import { ContactModalProps, ContactFormData } from "@/types/contact";
import {
  addContact,
  updateContact,
  deleteContact,
  closeModal,
  openModal,
} from "@/store/contactsSlice";

const ContactModal: React.FC<ContactModalProps> = ({
  isOpen,
  mode,
  contact,
  onClose,
  onSave,
  onDelete,
}) => {
  const dispatch = useDispatch();
  const [formData, setFormData] = useState<ContactFormData>({
    name: "",
    email: "",
    phone: "",
    address: "",
    notes: "",
    avatar: null,
  });
  const [avatarPreview, setAvatarPreview] = useState<string>("");
  const [errors, setErrors] = useState<Partial<ContactFormData>>({});

  useEffect(() => {
    if (contact && (mode === "edit" || mode === "view")) {
      setFormData({
        name: contact.name,
        email: contact.email,
        phone: contact.phone,
        address: contact.address || "",
        notes: contact.notes || "",
        avatar: null,
      });
      setAvatarPreview(contact.avatar || "");
    } else {
      setFormData({
        name: "",
        email: "",
        phone: "",
        address: "",
        notes: "",
        avatar: null,
      });
      setAvatarPreview("");
    }
    setErrors({});
  }, [contact, mode, isOpen]);

  const handleInputChange = (field: keyof ContactFormData, value: string) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors((prev) => ({ ...prev, [field]: undefined }));
    }
  };

  const handleAvatarChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];

    if (file) {
      setFormData((prev) => ({ ...prev, avatar: file }));
      const reader = new FileReader();

      reader.onload = () => {
        setAvatarPreview(reader.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Partial<ContactFormData> = {};

    if (!formData.name.trim()) {
      newErrors.name = "Name is required";
    }

    if (!formData.email.trim()) {
      newErrors.email = "Email is required";
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = "Please enter a valid email address";
    }

    if (!formData.phone.trim()) {
      newErrors.phone = "Phone number is required";
    }

    setErrors(newErrors);

    return Object.keys(newErrors).length === 0;
  };

  const handleSave = () => {
    if (!validateForm()) return;

    if (mode === "add") {
      dispatch(addContact(formData));
    } else if (mode === "edit" && contact) {
      dispatch(updateContact({ id: contact.id, data: formData }));
    }

    onSave(formData);
    handleClose();
  };

  const handleDelete = () => {
    if (
      contact &&
      window.confirm("Are you sure you want to delete this contact?")
    ) {
      dispatch(deleteContact(contact.id));
      if (onDelete) {
        onDelete(contact.id);
      }
      handleClose();
    }
  };

  const handleClose = () => {
    dispatch(closeModal());
    onClose();
  };

  const getModalTitle = () => {
    switch (mode) {
      case "add":
        return "Add New Contact";
      case "edit":
        return "Edit Contact";
      case "view":
        return "Contact Details";
      default:
        return "Contact";
    }
  };

  const isReadOnly = mode === "view";

  return (
    <Modal
      classNames={{
        base: "max-h-[90vh] bg-white text-gray-900 dark:bg-gray-800 dark:text-gray-100",
        header: "border-b border-gray-200 dark:border-gray-700",
        footer: "border-t border-gray-200 dark:border-gray-700",
        body: "py-6",
      }}
      isOpen={isOpen}
      scrollBehavior="inside"
      size="2xl"
      onClose={handleClose}
    >
      <ModalContent>
        <ModalHeader className="flex flex-col gap-1">
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-semibold">{getModalTitle()}</h2>
            {mode === "view" && contact && (
              <div className="flex gap-2">
                <Button
                  isIconOnly
                  className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
                  variant="light"
                  onPress={() => dispatch(openModal({ mode: "edit", contact }))}
                >
                  <Edit className="w-4 h-4" />
                </Button>
                <Button
                  isIconOnly
                  className="text-gray-500 hover:text-danger dark:text-gray-40 dark:hover:text-danger"
                  color="danger"
                  variant="light"
                  onPress={handleDelete}
                >
                  <Trash2 className="w-4 h-4" />
                </Button>
              </div>
            )}
          </div>
        </ModalHeader>
        <ModalBody>
          <div className="space-y-6">
            {/* Avatar Section */}
            <div className="flex flex-col items-center gap-4">
              <Avatar
                className="w-24 h-24"
                name={formData.name || contact?.name}
                size="lg"
                src={avatarPreview || contact?.avatar}
              />
              {!isReadOnly && (
                <div className="flex flex-col items-center gap-2">
                  <input
                    accept="image/*"
                    className="hidden"
                    id="avatar-upload"
                    type="file"
                    onChange={handleAvatarChange}
                  />
                  <Button
                    as="label"
                    htmlFor="avatar-upload"
                    size="sm"
                    startContent={<Upload className="w-4 h-4" />}
                    variant="bordered"
                  >
                    Upload Photo
                  </Button>
                </div>
              )}
            </div>

            <Divider />

            {/* Form Fields */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Input
                classNames={{
                  inputWrapper:
                    "bg-gray-50 border border-gray-200 dark:bg-gray-700 dark:border-gray-600",
                  label: "text-gray-700 dark:text-gray-300",
                }}
                errorMessage={errors.name}
                isInvalid={!!errors.name}
                isReadOnly={isReadOnly}
                isRequired={!isReadOnly}
                label="Full Name"
                placeholder="Enter full name"
                startContent={
                  <User className="w-4 h-4 text-gray-400 dark:text-gray-400" />
                }
                value={formData.name}
                onChange={(e) => handleInputChange("name", e.target.value)}
              />
              <Input
                classNames={{
                  inputWrapper:
                    "bg-gray-50 border border-gray-200 dark:bg-gray-700 dark:border-gray-600",
                  label: "text-gray-700 dark:text-gray-300",
                }}
                errorMessage={errors.email}
                isInvalid={!!errors.email}
                isReadOnly={isReadOnly}
                isRequired={!isReadOnly}
                label="Email Address"
                placeholder="Enter email address"
                startContent={
                  <Mail className="w-4 h-4 text-gray-400 dark:text-gray-400" />
                }
                type="email"
                value={formData.email}
                onChange={(e) => handleInputChange("email", e.target.value)}
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Input
                classNames={{
                  inputWrapper:
                    "bg-gray-50 border border-gray-200 dark:bg-gray-700 dark:border-gray-600",
                  label: "text-gray-700 dark:text-gray-300",
                }}
                errorMessage={errors.phone}
                isInvalid={!!errors.phone}
                isReadOnly={isReadOnly}
                isRequired={!isReadOnly}
                label="Phone Number"
                placeholder="Enter phone number"
                startContent={
                  <Phone className="w-4 h-4 text-gray-400 dark:text-gray-400" />
                }
                value={formData.phone}
                onChange={(e) => handleInputChange("phone", e.target.value)}
              />
              <Input
                classNames={{
                  inputWrapper:
                    "bg-gray-50 border border-gray-200 dark:bg-gray-700 dark:border-gray-600",
                  label: "text-gray-700 dark:text-gray-300",
                }}
                isReadOnly={isReadOnly}
                label="Address"
                placeholder="Enter address (optional)"
                startContent={
                  <MapPin className="w-4 h-4 text-gray-400 dark:text-gray-400" />
                }
                value={formData.address}
                onChange={(e) => handleInputChange("address", e.target.value)}
              />
            </div>

            <Textarea
              classNames={{
                inputWrapper:
                  "bg-gray-50 border border-gray-200 dark:bg-gray-700 dark:border-gray-600",
                label: "text-gray-700 dark:text-gray-300",
              }}
              isReadOnly={isReadOnly}
              label="Notes"
              maxRows={6}
              minRows={3}
              placeholder="Add any additional notes (optional)"
              startContent={
                <FileText className="w-4 h-4 text-gray-400 dark:text-gray-400" />
              }
              value={formData.notes}
              onChange={(e) => handleInputChange("notes", e.target.value)}
            />

            {/* Contact Info Display for View Mode */}
            {isReadOnly && contact && (
              <div className="mt-6">
                <Divider className="mb-4" />
                <div className="text-sm text-gray-500 space-y-2 dark:text-gray-400">
                  <p>
                    <strong>Created:</strong>{" "}
                    {dayjs(contact.createdAt).format("MMMM D, YYYY")}
                  </p>
                  <p>
                    <strong>Last Updated:</strong>{" "}
                    {dayjs(contact.updatedAt).format("MMMM D, YYYY")}
                  </p>
                </div>
              </div>
            )}
          </div>
        </ModalBody>
        <ModalFooter>
          <div className="flex justify-between w-full">
            <div>
              {mode === "edit" && contact && (
                <Button
                  className="text-gray-500 hover:text-danger dark:text-gray-400 dark:hover:text-danger"
                  color="danger"
                  startContent={<Trash2 className="w-4 h-4" />}
                  variant="light"
                  onPress={handleDelete}
                >
                  Delete
                </Button>
              )}
            </div>
            <div className="flex gap-2">
              <Button
                className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
                variant="light"
                onPress={handleClose}
              >
                {isReadOnly ? "Close" : "Cancel"}
              </Button>
              {!isReadOnly && (
                <Button color="primary" onPress={handleSave}>
                  {mode === "add" ? "Add Contact" : "Save Changes"}
                </Button>
              )}
            </div>
          </div>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
};

export default ContactModal;
