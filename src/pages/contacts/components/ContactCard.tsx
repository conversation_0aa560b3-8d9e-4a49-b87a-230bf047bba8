import React from "react";
import {
  <PERSON>,
  CardBody,
  Avatar,
  Button,
  Dropdown,
  DropdownTrigger,
  DropdownMenu,
  DropdownItem,
} from "@heroui/react";
import { MessageCircle, MoreVertical, Edit, Trash2 } from "lucide-react";

import { Contact } from "@/types/contact";
import { getInitials } from "@/utils/stringUtils.ts";

interface ContactCardProps {
  contact: Contact;
  onEdit: (contact: Contact) => void;
  onDelete: (id: string) => void;
}

const ContactCard: React.FC<ContactCardProps> = ({
  contact,
  onEdit,
  onDelete,
}) => {
  return (
    <Card className="w-full bg-white border border-gray-200 hover:shadow-lg transition-shadow duration-200 dark:bg-gray-800 dark:border-gray-700">
      <CardBody className="p-4">
        <div className="flex items-start gap-4">
          <Avatar
            className="flex-shrink-0"
            name={getInitials(contact.name)}
            size="lg"
          />

          <div className="flex-1 min-w-0">
            <h3 className="text-lg font-semibold text-foreground truncate">
              {contact.name}
            </h3>

            <p className="text-sm text-gray-500 mt-1 dark:text-gray-400">
              {contact.phone}
            </p>

            {contact.notes && (
              <p className="text-sm text-gray-500 mt-2 line-clamp-2 dark:text-gray-400">
                {contact.notes}
              </p>
            )}

            <div className="flex items-center justify-between mt-4">
              <Button
                color="primary"
                size="sm"
                startContent={<MessageCircle size={14} />}
              >
                Message
              </Button>

              <Dropdown>
                <DropdownTrigger>
                  <Button
                    isIconOnly
                    className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
                    size="sm"
                    variant="light"
                  >
                    <MoreVertical size={16} />
                  </Button>
                </DropdownTrigger>
                <DropdownMenu>
                  <DropdownItem
                    key="edit"
                    className="text-gray-700 dark:text-gray-300"
                    startContent={<Edit size={14} />}
                    onPress={() => onEdit(contact)}
                  >
                    Edit
                  </DropdownItem>
                  <DropdownItem
                    key="delete"
                    className="text-danger dark:text-danger-400"
                    color="danger"
                    startContent={<Trash2 size={14} />}
                    onPress={() => onDelete(contact.id)}
                  >
                    Delete
                  </DropdownItem>
                </DropdownMenu>
              </Dropdown>
            </div>
          </div>
        </div>
      </CardBody>
    </Card>
  );
};

export default ContactCard;
