import React from "react";
import { Input } from "@heroui/react";
import { Search, X } from "lucide-react";

import { SearchBarProps } from "@/types/contact";

const SearchBar: React.FC<SearchBarProps> = ({
  value,
  onChange,
  placeholder = "Search...",
}) => {
  const handleClear = () => {
    onChange("");
  };

  return (
    <Input
      classNames={{
        base: "max-w-full",
        mainWrapper: "h-full",
        input:
          "text-small text-gray-900 placeholder:text-gray-400 dark:text-gray-100 dark:placeholder:text-gray-400",
        inputWrapper:
          "h-full font-normal bg-gray-50 border border-gray-200 dark:bg-gray-700 dark:border-gray-600",
      }}
      endContent={
        value && (
          <button
            aria-label="Clear search"
            className="focus:outline-none"
            type="button"
            onClick={handleClear}
          >
            <X className="w-4 h-4 text-gray-400 hover:text-gray-600 dark:text-gray-400 dark:hover:text-gray-200 transition-colors" />
          </button>
        )
      }
      placeholder={placeholder}
      startContent={
        <Search className="w-4 h-4 text-gray-400 dark:text-gray-400 pointer-events-none flex-shrink-0" />
      }
      type="text"
      value={value}
      onChange={(e) => onChange(e.target.value)}
    />
  );
};

export default SearchBar;
