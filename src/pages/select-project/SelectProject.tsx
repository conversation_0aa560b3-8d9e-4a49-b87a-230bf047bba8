import React, { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { <PERSON><PERSON>, <PERSON>, CardBody, Spinner } from "@heroui/react";
import { <PERSON>olderKanban, FolderPlus, Grid3X3, List, Search } from "lucide-react";

import SearchBar from "./components/SearchBar";
import ProjectList from "./components/ProjectList";

import HeaderLayout from "@/layouts/HeaderLayout";
import { getProjects, Project } from "@/services/main/projectMainService";

const SelectProject: React.FC = () => {
  const navigate = useNavigate();
  const [projects, setProjects] = useState<Project[]>([]);
  const [filteredProjects, setFilteredProjects] = useState<Project[]>([]);
  const [searchQuery, setSearchQuery] = useState("");
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid");
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchProjects = async () => {
      try {
        setLoading(true);
        setError(null);

        const response = await getProjects();

        if (response.data.data) {
          setProjects(response.data.data);
          setFilteredProjects(response.data.data);
        }
      } catch (err: any) {
        // Provide more specific error messages based on the error type
        if (err.response) {
          // Server responded with error status
          setError(
            `Server error: ${err.response.status} - ${err.response.statusText}`,
          );
        } else if (err.request) {
          // Request was made but no response received
          setError(
            "Network error: Unable to connect to the server. Please check your connection.",
          );
        } else {
          // Something else happened
          setError(
            `Error: ${err.message || "Failed to load projects. Please try again."}`,
          );
        }
      } finally {
        setLoading(false);
      }
    };

    fetchProjects();
  }, []);

  useEffect(() => {
    // Filter projects based on search query
    if (!searchQuery) {
      setFilteredProjects(projects);
    } else {
      const query = searchQuery.toLowerCase();
      const filtered = projects.filter(
        (project) =>
          project.projectName.toLowerCase().includes(query) ||
          project.description.toLowerCase().includes(query),
      );

      setFilteredProjects(filtered);
    }
  }, [searchQuery, projects]);

  const handleProjectSelect = (project: Project) => {
    navigate(`/project/${project.uid}/chats`);
  };

  const handleCreateProject = () => {
    // TODO: Implement create project functionality
  };

  if (loading) {
    return (
      <HeaderLayout>
        <div className="flex min-h-screen items-center justify-center bg-gray-50 transition-all duration-200 ease-out dark:bg-gray-900">
          <Spinner size="lg" />
        </div>
      </HeaderLayout>
    );
  }

  if (error) {
    return (
      <HeaderLayout>
        <div className="flex min-h-screen items-center justify-center bg-gray-50 transition-all duration-200 ease-out dark:bg-gray-900">
          <div className="text-center">
            <p className="text-red-600 dark:text-red-400 mb-4">{error}</p>
            <Button onPress={() => window.location.reload()}>Retry</Button>
          </div>
        </div>
      </HeaderLayout>
    );
  }

  return (
    <HeaderLayout>
      <div className="flex-1 overflow-y-auto bg-gray-50 transition-all duration-200 ease-out dark:bg-gray-900">
        <div className="mx-auto max-w-6xl space-y-6 px-4 py-8 sm:px-6 lg:px-8">
          {/* Header */}
          <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
            <div className="flex items-center gap-3">
              <FolderKanban className="h-7 w-7 text-gray-700 dark:text-gray-300" />
              <div>
                <h1 className="text-3xl font-semibold text-gray-900 dark:text-gray-100">
                  Projects
                </h1>
                <p className="mt-1 text-gray-600 dark:text-gray-400">
                  Select a project to continue ({filteredProjects.length} total)
                </p>
              </div>
            </div>
            <Button
              className="w-full shadow-sm sm:w-auto"
              color="primary"
              isDisabled={true}
              startContent={<FolderPlus className="h-4 w-4" />}
              onPress={handleCreateProject}
            >
              Create Project
            </Button>
          </div>

          {/* Search and View Controls */}
          <Card className="border border-gray-200 bg-white shadow-sm dark:border-gray-700 dark:bg-gray-800">
            <CardBody className="flex flex-col gap-4 p-4 sm:flex-row sm:items-center sm:justify-between">
              <div className="w-full flex-1 sm:max-w-md">
                <SearchBar
                  placeholder="Search projects by name, description, or tags..."
                  value={searchQuery}
                  onChange={setSearchQuery}
                />
              </div>
              <div className="flex gap-2">
                <Button
                  isIconOnly
                  aria-label="Grid view"
                  aria-pressed={viewMode === "grid"}
                  className="transition-colors duration-200"
                  color={viewMode === "grid" ? "primary" : "default"}
                  variant={viewMode === "grid" ? "solid" : "light"}
                  onPress={() => setViewMode("grid")}
                >
                  <Grid3X3 className="h-4 w-4" />
                </Button>
                <Button
                  isIconOnly
                  aria-label="List view"
                  aria-pressed={viewMode === "list"}
                  className="transition-colors duration-200"
                  color={viewMode === "list" ? "primary" : "default"}
                  variant={viewMode === "list" ? "solid" : "light"}
                  onPress={() => setViewMode("list")}
                >
                  <List className="h-4 w-4" />
                </Button>
              </div>
            </CardBody>
          </Card>

          {/* Projects List */}
          {filteredProjects.length === 0 ? (
            <Card className="border border-gray-200 bg-white shadow-sm dark:border-gray-700 dark:bg-gray-800">
              <CardBody className="py-12 text-center">
                <Search className="mx-auto mb-4 h-16 w-16 text-gray-300 dark:text-gray-600" />
                <h3 className="mb-2 text-xl font-semibold text-gray-900 dark:text-gray-100">
                  {searchQuery ? "No projects found" : "No projects yet"}
                </h3>
                <p className="mb-6 text-gray-600 dark:text-gray-400">
                  {searchQuery
                    ? "Try adjusting your search terms"
                    : "Get started by creating your first project"}
                </p>
                {!searchQuery && (
                  <Button
                    color="primary"
                    isDisabled={true}
                    onPress={handleCreateProject}
                  >
                    Create Your First Project
                  </Button>
                )}
              </CardBody>
            </Card>
          ) : (
            <ProjectList
              projects={filteredProjects}
              viewMode={viewMode}
              onProjectSelect={handleProjectSelect}
            />
          )}
        </div>
      </div>
    </HeaderLayout>
  );
};

export default SelectProject;
