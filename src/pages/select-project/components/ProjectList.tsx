import React from "react";
import { Card, CardBody } from "@heroui/react";
import { FolderOpen } from "lucide-react";

import ProjectCard from "./ProjectCard";
import ProjectLogo from "./ProjectLogo";

import { ProjectListProps } from "@/types/project";

const ProjectList: React.FC<ProjectListProps> = ({
  projects,
  viewMode,
  onProjectSelect,
}) => {
  if (projects.length === 0) {
    return (
      <Card>
        <CardBody className="text-center py-12">
          <FolderOpen className="w-16 h-16 text-default-300 mx-auto mb-4" />
          <h3 className="text-xl font-semibold text-default-600 mb-2">
            No projects found
          </h3>
          <p className="text-default-400">
            Try adjusting your search terms or create a new project
          </p>
        </CardBody>
      </Card>
    );
  }

  if (viewMode === "grid") {
    return (
      <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-2 xl:grid-cols-2">
        {projects.map((project) => (
          <ProjectCard
            key={project.uid}
            project={project}
            onProjectSelect={onProjectSelect}
          />
        ))}
      </div>
    );
  }

  // List view
  return (
    <div className="space-y-3">
      {projects.map((project) => (
        <Card
          key={project.uid}
          isPressable
          className="border border-gray-200 bg-white transition-shadow duration-200 hover:shadow-md dark:border-gray-700 dark:bg-gray-800"
          onPress={() => onProjectSelect(project)}
        >
          <CardBody className="p-4">
            <div className="flex items-center justify-between">
              <ProjectLogo name={project.projectName} />
              <div className="flex-1 min-w-0 ml-3">
                <div className="flex flex-col gap-2 sm:flex-row sm:items-center sm:justify-between">
                  <div className="min-w-0">
                    <h3 className="truncate font-semibold text-gray-900 dark:text-gray-100">
                      {project.projectName}
                    </h3>
                    <p className="line-clamp-1 text-sm text-gray-600 dark:text-gray-300">
                      {project.description}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </CardBody>
        </Card>
      ))}
    </div>
  );
};

export default ProjectList;
