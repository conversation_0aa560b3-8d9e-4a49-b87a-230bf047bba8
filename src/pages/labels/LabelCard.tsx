import React, { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> } from "@heroui/react";
import { Edit, Trash2 } from "lucide-react";
import { useOutletContext } from "react-router-dom";

import LabelFormModal from "./LabelFormModal";

import { LabelModel } from "@/types/firestore/labelModel";
import { formatDate } from "@/utils/dateUtils";
import { useModal } from "@/hooks/useModal";
import { useAppDispatch, useAppSelector } from "@/store/hooks";
import { updateExistingLabel, deleteExistingLabel } from "@/store/labelsSlice";

interface Project {
  uid: string;
  projectName: string;
  description: string;
  createdAt: string;
  updatedAt: string;
  whatsappCredentials: any;
  ownerUserUid: string;
  userAccess: any[];
}

interface LabelCardProps {
  label: LabelModel;
}

export const LabelCard: React.FC<LabelCardProps> = ({ label }) => {
  const [isFormModalO<PERSON>, setIsFormModalOpen] = useState(false);
  const [isEditLoading, setIsEditLoading] = useState(false);
  const [isDeleteLoading, setIsDeleteLoading] = useState(false);
  const dispatch = useAppDispatch();
  const { showConfirm, showError, showSuccess } = useModal();
  const { project } = useOutletContext<{ project?: Project }>();

  const { loading: labelsLoading } = useAppSelector((state) => state.labels);

  const handleEditLabel = () => {
    setIsFormModalOpen(true);
  };

  const handleSaveEdit = async (formData: {
    name: string;
    description: string;
  }) => {
    if (!project) {
      showError({
        title: "Project Not Found",
        message: "Cannot update label without an active project.",
        confirmText: "OK",
      });

      return;
    }

    setIsEditLoading(true);

    try {
      await dispatch(
        updateExistingLabel({
          projectId: project.uid,
          labelId: label.id,
          labelData: {
            name: formData.name,
            description: formData.description,
          },
        }),
      ).unwrap();

      showSuccess({
        title: "Success",
        message: `Label "${formData.name}" has been updated.`,
        confirmText: "OK",
      });

      setIsFormModalOpen(false);
    } catch {
      // Log error for debugging
      // In production, consider using a proper error tracking service
      showError({
        title: "Failed to Update Label",
        message: `An error occurred while updating label "${label.name}". Please try again.`,
        confirmText: "OK",
      });
    } finally {
      setIsEditLoading(false);
    }
  };

  const handleDeleteLabel = async () => {
    if (!project) {
      showError({
        title: "Project Not Found",
        message: "Cannot delete label without an active project.",
        confirmText: "OK",
      });

      return;
    }

    showConfirm({
      title: "Delete Label",
      message: `Are you sure you want to delete "${label.name}"? This action cannot be undone.`,
      onConfirm: async () => {
        setIsDeleteLoading(true);
        try {
          await dispatch(
            deleteExistingLabel({
              projectId: project.uid,
              labelId: label.id,
            }),
          ).unwrap();

          showSuccess({
            title: "Success",
            message: `Label "${label.name}" has been deleted.`,
            confirmText: "OK",
          });
        } catch {
          // Log error for debugging
          // In production, consider using a proper error tracking service
          showError({
            title: "Failed to Delete Label",
            message: `An error occurred while deleting label "${label.name}". Please try again.`,
            confirmText: "OK",
          });
        } finally {
          setIsDeleteLoading(false);
        }
      },
      confirmText: "Delete",
      cancelText: "Cancel",
    });
  };

  // Determine if any operation is currently loading
  const isLoading = isEditLoading || isDeleteLoading || labelsLoading;

  return (
    <>
      <Card className="border border-gray-200 bg-white dark:border-gray-700 dark:bg-gray-800 shadow-sm hover:shadow-md transition-shadow duration-200">
        <CardBody className="p-4">
          <div className="flex justify-between items-start">
            <div className="flex-1 min-w-0">
              <div className="flex items-center gap-2 mb-1">
                <span className="text-xs font-medium text-gray-500 dark:text-gray-400 bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded">
                  ID: {label.id}
                </span>
              </div>
              <h3 className="font-semibold text-gray-900 dark:text-gray-100 truncate">
                {label.name}
              </h3>
              <p className="text-sm text-gray-600 dark:text-gray-300 mt-1 line-clamp-2">
                {label.description}
              </p>
            </div>
          </div>

          <div className="mt-3 grid grid-cols-2 gap-2 text-sm">
            <div>
              <span className="text-gray-500 dark:text-gray-400">Created:</span>
              <p className="text-gray-900 dark:text-gray-200">
                {formatDate(label.createdAt)}
              </p>
            </div>
            <div>
              <span className="text-gray-50 dark:text-gray-400">Updated:</span>
              <p className="text-gray-90 dark:text-gray-20">
                {formatDate(label.updatedAt)}
              </p>
            </div>
          </div>
        </CardBody>
        <CardFooter className="p-4 pt-0 flex justify-end gap-2">
          <Button
            isIconOnly
            className="text-gray-500 hover:text-primary dark:text-gray-400 dark:hover:text-primary"
            isDisabled={isLoading}
            size="sm"
            variant="light"
            onPress={handleEditLabel}
          >
            <Edit className="w-4 h-4" />
          </Button>
          <Button
            isIconOnly
            className="text-gray-500 hover:text-danger dark:text-gray-400 dark:hover:text-danger"
            isDisabled={isLoading}
            size="sm"
            variant="light"
            onPress={handleDeleteLabel}
          >
            <Trash2 className="w-4 h-4" />
          </Button>
        </CardFooter>
      </Card>
      <LabelFormModal
        initialData={{
          id: label.id,
          name: label.name,
          description: label.description,
        }}
        isOpen={isFormModalOpen}
        mode="edit"
        onClose={() => setIsFormModalOpen(false)}
        onSave={handleSaveEdit}
      />
    </>
  );
};
