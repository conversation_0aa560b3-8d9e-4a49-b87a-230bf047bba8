import React, { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  Input,
  Textarea,
} from "@heroui/react";
import { Tag } from "lucide-react";
import { useOutletContext } from "react-router-dom";

import { useAppDispatch } from "@/store/hooks";
import {
  createNewLabel,
  updateExistingLabel,
  fetchAllLabels,
} from "@/store/labelsSlice";
import { useModal } from "@/hooks/useModal.ts";

interface Project {
  uid: string;
  projectName: string;
  description: string;
  createdAt: string;
  updatedAt: string;
  whatsappCredentials: any;
  ownerUserUid: string;
  userAccess: any[];
}

interface LabelFormData {
  name: string;
  description: string;
}

interface LabelFormModalProps {
  isOpen: boolean;
  mode: "create" | "edit";
  initialData?: {
    id: string;
    name: string;
    description: string;
  };
  onClose: () => void;
  onSave?: (formData: { name: string; description: string }) => void;
}

const LabelFormModal: React.FC<LabelFormModalProps> = ({
  isOpen,
  mode,
  initialData,
  onClose,
  onSave,
}) => {
  const [formData, setFormData] = useState<LabelFormData>({
    name: "",
    description: "",
  });
  const [errors, setErrors] = useState<Partial<LabelFormData>>({});
  const [isSaving, setIsSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const dispatch = useAppDispatch();
  const { project } = useOutletContext<{ project?: Project }>();
  const { showSuccess } = useModal();

  useEffect(() => {
    if (initialData && mode === "edit") {
      setFormData({
        name: initialData.name,
        description: initialData.description,
      });
    } else {
      setFormData({
        name: "",
        description: "",
      });
    }
    setErrors({});
    setError(null);
  }, [initialData, mode, isOpen]);

  const handleInputChange = (field: keyof LabelFormData, value: string) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors((prev) => ({ ...prev, [field]: undefined }));
    }
    if (error) {
      setError(null);
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Partial<LabelFormData> = {};

    if (!formData.name.trim()) {
      newErrors.name = "Name is required";
    }

    setErrors(newErrors);

    return Object.keys(newErrors).length === 0;
  };

  const handleSave = async () => {
    if (!validateForm()) return;

    // If onSave callback is provided, use it instead of dispatching actions directly
    if (onSave) {
      setIsSaving(true);
      try {
        await onSave(formData);
        handleClose();
      } catch {
        // Log error for debugging
        // In production, consider using a proper error tracking service
        setError("Failed to save label");
      } finally {
        setIsSaving(false);
      }

      return;
    }

    if (!project) {
      setError("Project not found. Cannot save label.");

      return;
    }

    setIsSaving(true);
    setError(null);

    try {
      if (mode === "create") {
        await dispatch(
          createNewLabel({
            projectId: project.uid,
            labelData: {
              name: formData.name,
              description: formData.description,
            },
          }),
        ).unwrap();

        // Show success dialog for create operation
        showSuccess({
          title: "Success",
          message: `Label "${formData.name}" has been created successfully.`,
          confirmText: "OK",
        });
      } else if (mode === "edit" && initialData) {
        await dispatch(
          updateExistingLabel({
            projectId: project.uid,
            labelId: initialData.id,
            labelData: {
              name: formData.name,
              description: formData.description,
            },
          }),
        ).unwrap();

        // Show success dialog for edit operation
        showSuccess({
          title: "Success",
          message: `Label "${formData.name}" has been updated successfully.`,
          confirmText: "OK",
        });
      }

      // Refresh the labels list after successful create or update
      await dispatch(fetchAllLabels({ projectId: project.uid })).unwrap();

      handleClose();
    } catch (err) {
      // Log error for debugging
      // In production, consider using a proper error tracking service
      console.error("Error saving label:", err);
      setError("Failed to save label");
    } finally {
      setIsSaving(false);
    }
  };

  const handleClose = () => {
    onClose();
  };

  const getModalTitle = () => {
    switch (mode) {
      case "create":
        return "Create New Label";
      case "edit":
        return "Edit Label";
      default:
        return "Label";
    }
  };

  return (
    <Modal
      classNames={{
        base: "max-h-[90vh] bg-white text-gray-900 dark:bg-gray-800 dark:text-gray-100",
        header: "border-b border-gray-200 dark:border-gray-700",
        footer: "border-t border-gray-200 dark:border-gray-700",
        body: "py-6",
      }}
      isOpen={isOpen}
      scrollBehavior="inside"
      size="md"
      onClose={handleClose}
    >
      <ModalContent>
        <ModalHeader className="flex flex-col gap-1">
          <div className="flex items-center gap-2">
            <Tag className="w-5 h-5" />
            <h2 className="text-xl font-semibold">{getModalTitle()}</h2>
          </div>
        </ModalHeader>
        <ModalBody>
          <div className="space-y-6">
            {error && (
              <div className="p-3 bg-red-50 border-red-200 rounded-md text-red-600 text-sm">
                {error}
              </div>
            )}
            {/* Form Fields */}
            <div className="space-y-4">
              {/* ID Field - only shown in edit mode */}
              {mode === "edit" && initialData && (
                <Input
                  isReadOnly
                  classNames={{
                    inputWrapper:
                      "bg-gray-100 border border-gray-200 dark:bg-gray-700 dark:border-gray-600",
                    label: "text-gray-700 dark:text-gray-300",
                    input: "text-gray-900 dark:text-gray-100 font-mono text-sm",
                  }}
                  description="This is the unique identifier for this label"
                  label="Label ID"
                  value={initialData.id}
                />
              )}
              <Input
                isRequired
                classNames={{
                  inputWrapper:
                    "bg-gray-50 border border-gray-200 dark:bg-gray-700 dark:border-gray-600",
                  label: "text-gray-700 dark:text-gray-300",
                }}
                errorMessage={errors.name}
                isInvalid={!!errors.name}
                label="Label Name"
                placeholder="Enter label name"
                value={formData.name}
                onChange={(e) => handleInputChange("name", e.target.value)}
              />
              <Textarea
                classNames={{
                  inputWrapper:
                    "bg-gray-50 border border-gray-200 dark:bg-gray-700 dark:border-gray-600",
                  label: "text-gray-700 dark:text-gray-300",
                }}
                label="Description"
                placeholder="Enter label description (optional)"
                value={formData.description}
                onChange={(e) =>
                  handleInputChange("description", e.target.value)
                }
              />
            </div>
          </div>
        </ModalBody>
        <ModalFooter>
          <div className="flex justify-end gap-2 w-full">
            <Button
              className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
              isDisabled={isSaving}
              variant="light"
              onPress={handleClose}
            >
              Cancel
            </Button>
            <Button color="primary" isLoading={isSaving} onPress={handleSave}>
              {mode === "create" ? "Create Label" : "Save Changes"}
            </Button>
          </div>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
};

export default LabelFormModal;
export type { LabelFormModalProps, LabelFormData };
