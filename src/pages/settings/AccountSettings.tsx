import { ArrowLeft } from "lucide-react";
import { useNavigate } from "react-router-dom";

import ProfileSection from "./components/ProfileSection";
import AppearanceSection from "./components/AppearanceSection";
import AboutSection from "./components/AboutSection";

import HeaderLayout from "@/layouts/HeaderLayout";

const AccountSettings = () => {
  const navigate = useNavigate();

  const handleBack = () => {
    navigate(-1);
  };

  return (
    <HeaderLayout>
      <div className="flex-1 overflow-y-auto">
        <div className="max-w-4xl mx-auto p-6 space-y-8">
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center space-x-4">
              <button
                aria-label="Go back"
                className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
                onClick={handleBack}
              >
                <ArrowLeft className="w-5 h-5 text-gray-600 dark:text-gray-400" />
              </button>
              <div>
                <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
                  Account Settings
                </h1>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  Manage your account preferences and app settings
                </p>
              </div>
            </div>
          </div>

          <div className="space-y-6">
            <ProfileSection />
            <AppearanceSection />
            <AboutSection />
          </div>
        </div>
      </div>
    </HeaderLayout>
  );
};

export default AccountSettings;
