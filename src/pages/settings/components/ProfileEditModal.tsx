import React, { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  <PERSON>dal<PERSON>ody,
  <PERSON>dal<PERSON>oot<PERSON>,
  <PERSON><PERSON>,
  Input,
} from "@heroui/react";

interface ProfileEditModalProps {
  isOpen: boolean;
  initialName?: string;
  onClose: () => void;
  onSave: (name: string) => void;
}

const ProfileEditModal: React.FC<ProfileEditModalProps> = ({
  isOpen,
  initialName = "",
  onClose,
  onSave,
}) => {
  const [name, setName] = useState<string>(initialName);

  // Reset form when modal opens or initialName changes
  useEffect(() => {
    if (isOpen) {
      setName(initialName);
    }
  }, [isOpen, initialName]);

  const handleSave = () => {
    onSave(name);
    onClose();
  };

  const handleClose = () => {
    setName(initialName);
    onClose();
  };

  return (
    <Modal
      classNames={{
        base: "bg-white text-gray-900 dark:bg-gray-800 dark:text-gray-100",
        header: "border-b border-gray-200 dark:border-gray-700",
        footer: "border-t border-gray-200 dark:border-gray-700",
      }}
      isOpen={isOpen}
      onClose={handleClose}
    >
      <ModalContent>
        <ModalHeader>
          <h2 className="text-xl font-semibold">Edit Profil</h2>
        </ModalHeader>
        <ModalBody className="py-6">
          <Input
            classNames={{
              inputWrapper:
                "bg-gray-50 border border-gray-200 dark:bg-gray-700 dark:border-gray-600",
              label: "text-gray-700 dark:text-gray-300",
            }}
            label="Nama"
            placeholder="Masukkan nama"
            value={name}
            onChange={(e) => setName(e.target.value)}
          />
        </ModalBody>
        <ModalFooter>
          <div className="flex gap-2">
            <Button
              className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
              variant="light"
              onPress={handleClose}
            >
              Batal
            </Button>
            <Button color="primary" onPress={handleSave}>
              Simpan
            </Button>
          </div>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
};

export default ProfileEditModal;
