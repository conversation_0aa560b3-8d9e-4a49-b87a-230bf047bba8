import React, { useEffect, useState } from "react";
import { <PERSON><PERSON>, Input, <PERSON><PERSON>, Textarea } from "@heroui/react";

import { Project, updateProject } from "@/services/main/projectMainService";

interface ProjectOverviewSectionProps {
  project: Project | null;
}

interface FormData {
  projectName: string;
  description: string;
}

interface FormErrors {
  projectName?: string;
  description?: string;
  general?: string;
}

const ProjectOverviewSection = ({ project }: ProjectOverviewSectionProps) => {
  const [formData, setFormData] = useState<FormData>({
    projectName: "",
    description: "",
  });
  const [errors, setErrors] = useState<FormErrors>({});
  const [isLoading, setIsLoading] = useState(false);
  const [successMessage, setSuccessMessage] = useState("");

  useEffect(() => {
    if (project) {
      setFormData({
        projectName: project.projectName,
        description: project.description,
      });
    }
  }, [project]);

  const handleInputChange = (field: keyof FormData) => (value: string) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
    // Clear field-specific error when user starts typing
    if (errors[field]) {
      setErrors((prev) => ({ ...prev, [field]: undefined }));
    }
    // Clear success message
    if (successMessage) {
      setSuccessMessage("");
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!project) return;

    setIsLoading(true);
    setErrors({});
    setSuccessMessage("");

    try {
      await updateProject(project.uid, {
        projectName: formData.projectName,
        description: formData.description,
      });
      setSuccessMessage("Project updated successfully!");
    } catch (error: any) {
      if (error.response?.status === 422 && error.response?.data?.errors) {
        // Handle validation errors
        const fieldErrors: FormErrors = {};

        error.response.data.errors.forEach((err: any) => {
          const field = err.path?.[0];

          if (field === "projectName") {
            fieldErrors.projectName = err.message;
          } else if (field === "description") {
            fieldErrors.description = err.message;
          }
        });
        setErrors(fieldErrors);
      } else {
        // Handle network or other errors
        setErrors({ general: "Failed to update project. Please try again." });
      }
    } finally {
      setIsLoading(false);
    }
  };

  if (!project) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
        <div className="flex justify-center items-center py-8">
          <Spinner size="lg" />
          <span className="ml-2 text-gray-600 dark:text-gray-400">
            Loading project...
          </span>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
      <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
        Project Overview
      </h2>

      <form className="space-y-4" onSubmit={handleSubmit}>
        <div>
          <label
            className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"
            htmlFor="project-name"
          >
            Project Name
          </label>
          <Input
            className="w-full"
            errorMessage={errors.projectName}
            id="project-name"
            isInvalid={!!errors.projectName}
            placeholder="Enter project name"
            value={formData.projectName}
            onValueChange={handleInputChange("projectName")}
          />
        </div>

        <div>
          <label
            className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"
            htmlFor="project-description"
          >
            Description
          </label>
          <Textarea
            className="w-full"
            errorMessage={errors.description}
            id="project-description"
            isInvalid={!!errors.description}
            placeholder="Describe the purpose of this project"
            rows={4}
            value={formData.description}
            onValueChange={handleInputChange("description")}
          />
        </div>

        {errors.general && (
          <div className="text-red-600 dark:text-red-400 text-sm">
            {errors.general}
          </div>
        )}

        {successMessage && (
          <div className="text-green-600 dark:text-green-400 text-sm">
            {successMessage}
          </div>
        )}

        <div className="mt-6 flex justify-end">
          <Button
            color="primary"
            disabled={isLoading}
            isLoading={isLoading}
            size="sm"
            type="submit"
          >
            {isLoading ? "Saving..." : "Save Changes"}
          </Button>
        </div>
      </form>
    </div>
  );
};

export default ProjectOverviewSection;
