import { Info } from "lucide-react";

interface SectionProps {
  className?: string;
}

export default function AboutSection({ className = "" }: SectionProps) {
  const appVersion = import.meta.env.VITE_WEB_APP_VERSION;
  const buildNumber = import.meta.env.VITE_WEB_APP_BUILD_NUMBER;

  return (
    <div
      className={`bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm ${className}`}
    >
      <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
        About
      </h2>

      <div className="space-y-6">
        {/* App Information */}
        <div className="space-y-3">
          <div className="flex items-center space-x-3">
            <Info className="w-5 h-5 text-blue-500" />
            <div>
              <p className="text-sm font-medium text-gray-900 dark:text-white">
                Ideal Lumatera
              </p>
              <p className="text-xs text-gray-600 dark:text-gray-400">
                A modern chat application
              </p>
            </div>
          </div>

          <div className="ml-8 space-y-1">
            <p className="text-xs text-gray-600 dark:text-gray-400">
              Version: <span className="font-mono">{appVersion}</span>
            </p>
            <p className="text-xs text-gray-600 dark:text-gray-400">
              Build: <span className="font-mono">{buildNumber}</span>
            </p>
          </div>
        </div>

        {/* Copyright */}
        <div className="pt-4 border-t border-gray-200 dark:border-gray-700">
          <p className="text-xs text-gray-500 dark:text-gray-500 text-center">
            © {import.meta.env.VITE_CURRENT_YEAR || "2025"} Ideal Lumatera. All
            rights reserved.
          </p>
        </div>
      </div>
    </div>
  );
}
