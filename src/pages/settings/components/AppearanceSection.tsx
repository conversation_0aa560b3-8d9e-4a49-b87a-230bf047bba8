import { Moon, Sun } from "lucide-react";
import { Switch } from "@heroui/react";

import { useAppDispatch, useAppSelector } from "@/store/hooks";
import { toggleTheme } from "@/store/uiSlice";

interface SectionProps {
  className?: string;
}

export default function AppearanceSection({ className = "" }: SectionProps) {
  const { theme } = useAppSelector((state) => state.ui);
  const dispatch = useAppDispatch();

  const handleThemeToggle = () => {
    dispatch(toggleTheme());
  };

  return (
    <div
      className={`bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm ${className}`}
    >
      <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
        Appearance
      </h2>

      <div className="space-y-6">
        {/* Theme Toggle */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            {theme === "light" ? (
              <Sun className="w-5 h-5 text-yellow-500" />
            ) : (
              <Moon className="w-5 h-5 text-blue-500" />
            )}
            <div>
              <p className="text-sm font-medium text-gray-900 dark:text-white">
                Dark Mode
              </p>
              <p className="text-xs text-gray-600 dark:text-gray-400">
                Toggle between light and dark theme
              </p>
            </div>
          </div>
          <Switch
            color="primary"
            isSelected={theme === "dark"}
            size="sm"
            onValueChange={handleThemeToggle}
          />
        </div>
      </div>
    </div>
  );
}
