import { <PERSON>, CardBody, CardHeader } from "@heroui/card";
import { MailIcon, UserIcon } from "lucide-react";

import { Project } from "@/services/main/projectMainService";
import { getInitials } from "@/utils/stringUtils";

interface ProjectUsersListProps {
  project: Project | null;
}

const ProjectUsersList = ({ project }: ProjectUsersListProps) => {
  const getRoleColor = (role: string) => {
    switch (role.toLowerCase()) {
      case "admin":
      case "owner":
        return "bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-200";
      case "editor":
        return "bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-200";
      case "viewer":
        return "bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200";
      default:
        return "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-200";
    }
  };

  const getInitialsBackgroundColor = (email: string) => {
    const colors = [
      "bg-blue-500",
      "bg-green-500",
      "bg-purple-500",
      "bg-pink-500",
      "bg-indigo-500",
      "bg-yellow-500",
      "bg-red-500",
      "bg-teal-500",
    ];
    const index = email.charCodeAt(0) % colors.length;

    return colors[index];
  };

  if (!project) {
    return (
      <Card className="bg-white border border-gray-200 dark:bg-gray-800 dark:border-gray-700">
        <CardHeader>
          <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
            Project Users
          </h3>
        </CardHeader>
        <CardBody>
          <div className="text-center py-8">
            <UserIcon className="w-12 h-12 text-gray-400 dark:text-gray-400 mx-auto mb-3" />
            <div className="text-gray-500 dark:text-gray-400">
              No project data available
            </div>
          </div>
        </CardBody>
      </Card>
    );
  }

  return (
    <Card className="bg-white border border-gray-200 dark:bg-gray-800 dark:border-gray-700">
      <CardHeader>
        <div className="flex items-center space-x-2">
          <UserIcon className="w-5 h-5 text-gray-600 dark:text-gray-400" />
          <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
            Project Users
          </h3>
        </div>
      </CardHeader>
      <CardBody>
        {project.userAccess && project.userAccess.users.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {project.userAccess.users.map((user) => (
              <div
                key={user.uid}
                className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors"
              >
                <div className="flex items-center space-x-3">
                  <div
                    className={`w-8 h-8 rounded-full flex items-center justify-center text-white font-semibold text-sm ${getInitialsBackgroundColor(
                      user.email,
                    )}`}
                  >
                    {getInitials(user.email.split("@")[0])}
                  </div>
                  <div>
                    <div className="flex items-center space-x-2">
                      <MailIcon className="w-3 h-3 text-gray-500 dark:text-gray-400" />
                      <span className="font-medium text-gray-900 dark:text-gray-100 text-sm">
                        {user.email}
                      </span>
                    </div>
                    <div className="flex items-center space-x-2 mt-1">
                      <span
                        className={`px-2 py-1 text-xs font-medium rounded-full ${getRoleColor(
                          user.role,
                        )}`}
                      >
                        {user.role.charAt(0).toUpperCase() + user.role.slice(1)}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-8">
            <UserIcon className="w-12 h-12 text-gray-400 dark:text-gray-400 mx-auto mb-4" />
            <p className="text-gray-500 dark:text-gray-400">
              No users have access to this project yet
            </p>
          </div>
        )}
      </CardBody>
    </Card>
  );
};

export default ProjectUsersList;
