import React from "react";
import { MessageCircle, Clock, User } from "lucide-react";

// Will be replaced with API calls later
interface RecentChat {
  id: string;
  contactName: string;
  lastMessage: string;
  timestamp: string;
  avatar?: string;
  isOnline: boolean;
}

interface RecentChatsPreviewProps {
  className?: string;
}

const RecentChatsPreview: React.FC<RecentChatsPreviewProps> = ({
  className = "",
}) => {
  const handleChatClick = (_chatId: string) => {
    // Navigate to specific chat - akan diintegrasikan dengan routing nanti
    // TODO: Implement chat navigation logic
  };

  const handleViewAllChats = () => {
    // Navigate to all chats page
    // TODO: Implement all chats navigation logic
  };

  return (
    <div
      className={`bg-white rounded-lg shadow-sm border border-gray-200 ${className} dark:bg-gray-800 dark:border-gray-700`}
    >
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-100 dark:border-gray-700">
        <div className="flex items-center gap-2">
          <MessageCircle className="w-5 h-5 text-blue-600" />
          <h3 className="font-semibold text-gray-900 dark:text-gray-100">
            Recent Chats
          </h3>
        </div>
        <button
          className="text-sm text-blue-600 hover:text-blue-700 font-medium transition-colors dark:text-blue-400 dark:hover:text-blue-300"
          onClick={handleViewAllChats}
        >
          View All
        </button>
      </div>

      {/* Chat List */}
      <div className="divide-y divide-gray-100 dark:divide-gray-700">
        {([] as RecentChat[]).slice(0, 4).map((chat: RecentChat) => (
          <div
            key={chat.id}
            className="p-4 hover:bg-gray-50 cursor-pointer transition-colors dark:hover:bg-gray-700/30"
            role="button"
            tabIndex={0}
            onClick={() => handleChatClick(chat.id)}
            onKeyDown={(e) => {
              if (e.key === "Enter" || e.key === " ") {
                handleChatClick(chat.id);
              }
            }}
          >
            <div className="flex items-start gap-3">
              {/* Avatar */}
              <div className="relative flex-shrink-0">
                <div className="w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center">
                  <User className="w-5 h-5 text-white" />
                </div>
                {chat.isOnline && (
                  <div className="absolute -bottom-0.5 -right-0.5 w-3 h-3 bg-green-500 border-2 border-white rounded-full" />
                )}
              </div>

              {/* Chat Info */}
              <div className="flex-1 min-w-0">
                <div className="flex items-center justify-between mb-1">
                  <h4 className="font-medium text-gray-900 truncate dark:text-gray-100">
                    {chat.contactName}
                  </h4>
                  <div className="flex items-center gap-2 flex-shrink-0">
                    <span className="text-xs text-gray-500 flex items-center gap-1 dark:text-gray-400">
                      <Clock className="w-3 h-3" />
                      {chat.timestamp}
                    </span>
                  </div>
                </div>
                <p className="text-sm text-gray-600 truncate dark:text-gray-400">
                  {chat.lastMessage}
                </p>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Footer */}
      {false && (
        <div className="p-4 border-t border-gray-100 text-center dark:border-gray-700">
          <button
            className="text-sm text-gray-600 hover:text-gray-800 transition-colors dark:text-gray-400 dark:hover:text-gray-200"
            onClick={handleViewAllChats}
          >
            +0 more chats
          </button>
        </div>
      )}
    </div>
  );
};

export default RecentChatsPreview;
