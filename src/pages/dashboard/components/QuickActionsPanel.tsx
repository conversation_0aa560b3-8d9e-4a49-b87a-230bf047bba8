import React from "react";
import { MessageCircle, Users, Search, Settings, Zap } from "lucide-react";

// Will be replaced with API calls later
interface QuickAction {
  id: string;
  label: string;
  icon: string;
  action: string; // action type for routing
  color?: string;
}

// Hardcoded quick actions
const quickActions: QuickAction[] = [
  {
    id: "1",
    label: "New Chat",
    icon: "MessageCircle",
    action: "/chats",
    color: "blue",
  },
  {
    id: "2",
    label: "Contacts",
    icon: "Users",
    action: "/contacts",
    color: "green",
  },
  {
    id: "3",
    label: "Search",
    icon: "Search",
    action: "/chats",
    color: "purple",
  },
  {
    id: "4",
    label: "Settings",
    icon: "Settings",
    action: "/settings",
    color: "gray",
  },
];

interface QuickActionsPanelProps {
  className?: string;
}

const QuickActionsPanel: React.FC<QuickActionsPanelProps> = ({
  className = "",
}) => {
  const getIconComponent = (iconName: string) => {
    const iconMap: { [key: string]: React.ComponentType<any> } = {
      MessageCircle,
      Users,
      Search,
      Settings,
    };

    return iconMap[iconName] || MessageCircle;
  };

  const getColorClasses = (color?: string) => {
    const colorMap: {
      [key: string]: { bg: string; hover: string; text: string };
    } = {
      blue: {
        bg: "bg-blue-50 dark:bg-blue-900/30",
        hover: "hover:bg-blue-100 dark:hover:bg-blue-800/50",
        text: "text-blue-600 dark:text-blue-300",
      },
      green: {
        bg: "bg-green-50 dark:bg-green-900/30",
        hover: "hover:bg-green-100 dark:hover:bg-green-800/50",
        text: "text-green-600 dark:text-green-300",
      },
      purple: {
        bg: "bg-purple-50 dark:bg-purple-900/30",
        hover: "hover:bg-purple-100 dark:hover:bg-purple-800/50",
        text: "text-purple-600 dark:text-purple-300",
      },
      gray: {
        bg: "bg-gray-50 dark:bg-gray-700/30",
        hover: "hover:bg-gray-100 dark:hover:bg-gray-600/50",
        text: "text-gray-600 dark:text-gray-300",
      },
    };

    return colorMap[color || "blue"];
  };

  const handleActionClick = (_action: QuickAction) => {
    // Navigate to action route - akan diintegrasikan dengan routing nanti
    // TODO: Implement navigation logic for
  };

  return (
    <div
      className={`bg-white rounded-lg shadow-sm border border-gray-200 ${className} dark:bg-gray-800 dark:border-gray-700`}
    >
      {/* Header */}
      <div className="flex items-center gap-2 p-4 border-b border-gray-100 dark:border-gray-700">
        <Zap className="w-5 h-5 text-yellow-500" />
        <h3 className="font-semibold text-gray-900 dark:text-gray-100">
          Quick Actions
        </h3>
      </div>

      {/* Actions Grid */}
      <div className="p-4">
        <div className="grid grid-cols-2 gap-3">
          {quickActions.map((action: QuickAction) => {
            const IconComponent = getIconComponent(action.icon);
            const colorClasses = getColorClasses(action.color);

            return (
              <button
                key={action.id}
                className={`
                  ${colorClasses.bg} ${colorClasses.hover}
                  p-4 rounded-lg transition-all duration-200
                  hover:shadow-md hover:scale-105
                  focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2
                  group dark:focus:ring-offset-gray-800
                `}
                onClick={() => handleActionClick(action)}
              >
                <div className="flex flex-col items-center gap-2">
                  <div
                    className={`
                    ${colorClasses.text}
                    group-hover:scale-110 transition-transform duration-200
                  `}
                  >
                    <IconComponent className="w-6 h-6" />
                  </div>
                  <span
                    className={`
                    text-sm font-medium ${colorClasses.text}
                    group-hover:font-semibold transition-all duration-200
                  `}
                  >
                    {action.label}
                  </span>
                </div>
              </button>
            );
          })}
        </div>
      </div>

      {/* Footer dengan tips */}
      <div className="px-4 pb-4">
        <div className="bg-gray-50 rounded-lg p-3 dark:bg-gray-700/30">
          <p className="text-xs text-gray-600 text-center dark:text-gray-300">
            💡 Tip: Use keyboard shortcuts for faster access
          </p>
        </div>
      </div>
    </div>
  );
};

export default QuickActionsPanel;
