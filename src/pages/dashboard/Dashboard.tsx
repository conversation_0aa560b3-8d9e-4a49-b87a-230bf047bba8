import React from "react";

import WelcomeSection from "./components/WelcomeSection";
import QuickActionsPanel from "./components/QuickActionsPanel";
import RecentChatsPreview from "./components/RecentChatsPreview";
import StatisticsSummary from "./components/StatisticsSummary";

import { useAppSelector } from "@/store/hooks";
import DefaultLayout from "@/layouts/default";

const Dashboard: React.FC = () => {
  const { visiblePanels, screenSize } = useAppSelector((state) => state.ui);

  // Calculate left margin - always maintain ml-14 when sidebar is visible on non-mobile screens
  const leftMargin =
    visiblePanels.mainMenu && screenSize !== "mobile" ? "ml-14" : "ml-0";

  return (
    <DefaultLayout>
      <div
        className={`h-full ${leftMargin} transition-all duration-200 ease-out`}
      >
        <div className="h-full overflow-y-auto bg-gray-50 dark:bg-gray-900">
          <div className="max-w-7xl mx-auto p-4 space-y-6">
            {/* Welcome Section */}
            <WelcomeSection className="col-span-full" />

            {/* Main Content Grid */}
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              {/* Left Column - Quick Actions & Stats */}
              <div className="lg:col-span-1 space-y-6">
                <QuickActionsPanel />
                <StatisticsSummary />
              </div>

              {/* Right Column - Recent Chats */}
              <div className="lg:col-span-2">
                <RecentChatsPreview />
              </div>
            </div>

            {/* Additional Content for Mobile */}
            {screenSize === "mobile" && (
              <div className="space-y-4">
                <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 dark:bg-gray-800 dark:border-gray-700">
                  <h3 className="font-semibold text-gray-900 mb-2 dark:text-gray-100">
                    Tips & Tricks
                  </h3>
                  <ul className="text-sm text-gray-600 space-y-1 dark:text-gray-300">
                    <li>• Swipe left on chats for quick actions</li>
                    <li>• Long press messages to react</li>
                    <li>• Use search to find old conversations</li>
                  </ul>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </DefaultLayout>
  );
};

export default Dashboard;
