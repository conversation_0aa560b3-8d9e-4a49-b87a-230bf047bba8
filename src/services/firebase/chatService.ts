import {
  collection,
  onSnapshot,
  orderBy,
  query,
  Unsubscribe,
} from "firebase/firestore";

import { firestore } from "@/services/firebase/firebase.ts";
import { ChatModel, ChatModelFirestore } from "@/types/firestore/chatModel.ts";

const subscribeToChats = (
  projectId: string,
  callback: (chats: ChatModel[]) => void,
  onError?: (error: Error) => void,
): Unsubscribe => {
  const chatsQuery = query(
    collection(firestore, "projects", projectId, "chats").withConverter(
      ChatModelFirestore.converter(),
    ),
    orderBy("lastMessage.message.timestamp", "desc"),
  );

  return onSnapshot(
    chatsQuery,
    (snapshot) => {
      const chats = snapshot.docs.map((doc) => {
        // The ChatModelFirestore converter already normalizes the data
        // So we can directly use the converted data
        return doc.data();
      });

      callback(chats);
    },
    (error) => {
      // Log error for debugging
      // In production, consider using a proper error tracking service
      onError?.(error);
    },
  );
};

export default subscribeToChats;
