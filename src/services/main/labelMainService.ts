import { AxiosResponse } from "axios";

import baseServiceEndpoint from "@/services/main/baseServiceEndpoint.ts";
import { LabelModel } from "@/types/firestore/labelModel.ts";

// Response interfaces
interface BaseResponse<T = any> {
  message: string;
  data: T;
}

interface GetAllLabelsResponse {
  message: string;
  data: LabelModel[];
}

interface CreateLabelRequest {
  name: string;
  description?: string;
}

interface UpdateLabelRequest {
  name?: string;
  description?: string;
}

// CRUD Operations for Labels

/**
 * Get all labels associated with a specific project
 * @param projectId The unique identifier of the project
 * @returns Promise containing all labels for the project
 */
export const getAllLabels = async (
  projectId: string,
): Promise<AxiosResponse<GetAllLabelsResponse>> => {
  const base = await baseServiceEndpoint();

  return base.get<GetAllLabelsResponse>(`/user/project/${projectId}/label`);
};

/**
 * Create a new label within a specific project
 * @param projectId The unique identifier of the project
 * @param labelData The label data to create
 * @returns Promise containing the created label
 */
export const createLabel = async (
  projectId: string,
  labelData: CreateLabelRequest,
): Promise<AxiosResponse<BaseResponse<LabelModel>>> => {
  const base = await baseServiceEndpoint();

  return base.post<BaseResponse<LabelModel>>(
    `/user/project/${projectId}/label`,
    labelData,
  );
};

/**
 * Update an existing label within a specific project
 * @param projectId The unique identifier of the project
 * @param labelId The unique identifier of the label to update
 * @param labelData The updated label data
 * @returns Promise containing the updated label
 */
export const updateLabel = async (
  projectId: string,
  labelId: string,
  labelData: UpdateLabelRequest,
): Promise<AxiosResponse<BaseResponse<LabelModel>>> => {
  const base = await baseServiceEndpoint();

  return base.put<BaseResponse<LabelModel>>(
    `/user/project/${projectId}/label/${labelId}`,
    labelData,
  );
};

/**
 * Delete an existing label within a specific project
 * @param projectId The unique identifier of the project
 * @param labelId The unique identifier of the label to delete
 * @returns Promise containing the deleted label ID
 */
export const deleteLabel = async (
  projectId: string,
  labelId: string,
): Promise<AxiosResponse<BaseResponse<{ id: string }>>> => {
  const base = await baseServiceEndpoint();

  return base.delete<BaseResponse<{ id: string }>>(
    `/user/project/${projectId}/label/${labelId}`,
  );
};
