import axios from "axios";

import { auth } from "@/services/firebase/firebase.ts";

const base = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL,
});

const baseServiceEndpoint = async () => {
  const getToken = await auth.currentUser?.getIdToken();

  if (getToken) {
    base.defaults.headers.common["Authorization"] =
      "Bearer " + (await auth.currentUser?.getIdToken());
  }

  return base;
};

export default baseServiceEndpoint;
