import { AxiosResponse } from "axios";

import baseServiceEndpoint from "@/services/main/baseServiceEndpoint.ts";
import { ChatModel } from "@/types/firestore/chatModel.ts";

// Response interfaces
interface BaseResponse<T = any> {
  message: string;
  data: T;
}

interface GetAllChatsResponse {
  message: string;
  data: ChatModel[];
}

interface UpdateChatLabelRequest {
  labelId?: string | null;
}

// CRUD Operations for Chats

/**
 * Get all chats associated with a specific project
 * @param projectId The unique identifier of the project
 * @returns Promise containing all chats for the project
 */
export const getAllChats = async (
  projectId: string,
): Promise<AxiosResponse<GetAllChatsResponse>> => {
  const base = await baseServiceEndpoint();

  return base.get<GetAllChatsResponse>(`/user/project/${projectId}/chat`);
};

/**
 * Update the label of a specific chat within a project
 * @param projectId The unique identifier of the project
 * @param chatId The unique identifier of the chat (typically phoneNumber)
 * @param labelData The label data to update
 * @returns Promise containing the updated chat
 */
export const updateChatLabel = async (
  projectId: string,
  chatId: string,
  labelData: UpdateChatLabelRequest,
): Promise<AxiosResponse<BaseResponse<ChatModel>>> => {
  const base = await baseServiceEndpoint();

  return base.put<BaseResponse<ChatModel>>(
    `/user/project/${projectId}/chat/${chatId}/label`,
    labelData,
  );
};
