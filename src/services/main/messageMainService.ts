import { AxiosResponse } from "axios";

import baseServiceEndpoint from "@/services/main/baseServiceEndpoint.ts";
import { WhatsAppMessageType } from "@/types/whatsapp.ts";

// Types for sending messages (different from receiving webhook types)
export interface TextMessageContent {
  body: string;
  previewUrl?: boolean;
}

export interface MediaMessageContent {
  id?: string;
  link?: string;
  caption?: string;
}

export interface DocumentMessageContent extends MediaMessageContent {
  filename: string;
}

export interface InteractiveButton {
  type: "reply";
  reply: {
    id: string;
    title: string;
  };
}

export interface InteractiveSectionRow {
  id: string;
  title: string;
  description?: string;
}

export interface InteractiveSection {
  title: string;
  rows: InteractiveSectionRow[];
}

export interface InteractiveMessageContent {
  type: "button" | "list" | "product" | "product_list";
  body: {
    text: string;
  };
  action: {
    buttons?: InteractiveButton[];
    button?: string;
    sections?: InteractiveSection[];
  };
}

export interface TemplateParameter {
  type: "text" | "currency" | "date_time" | "image";
  text?: string;
  [key: string]: any;
}

export interface TemplateComponent {
  type: "body" | "header" | "button";
  parameters?: TemplateParameter[];
}

export interface TemplateMessageContent {
  name: string;
  language: {
    code: string;
    policy: "deterministic";
  };
  components?: TemplateComponent[];
}

export interface MessagePayload {
  type: WhatsAppMessageType;
  text?: TextMessageContent;
  image?: MediaMessageContent;
  audio?: MediaMessageContent;
  video?: MediaMessageContent;
  document?: DocumentMessageContent;
  interactive?: InteractiveMessageContent;
  template?: TemplateMessageContent;
}

export interface SendMessageResponse {
  message: string;
  data: {
    message: {
      id: string;
      from: string;
      timestamp: string;
      type: WhatsAppMessageType;
      context: any | null;
      text?: TextMessageContent;
      image?: MediaMessageContent;
      audio?: MediaMessageContent;
      video?: MediaMessageContent;
      document?: DocumentMessageContent;
      interactive?: InteractiveMessageContent;
      template?: TemplateMessageContent;
      statuses: any | null;
      direction: "out";
      createdAt: string;
      sentBy: {
        uid: string;
        email: string;
        name: string;
      };
    };
  };
}

export const sendMessage = async (
  projectId: string,
  chatId: string,
  message: MessagePayload,
): Promise<AxiosResponse<SendMessageResponse>> => {
  const base = await baseServiceEndpoint();

  return base.post<SendMessageResponse>(
    `/user/project/${projectId}/chat/${chatId}/messages`,
    message,
  );
};

export interface GetMessageMediaOptions {
  asJson?: boolean;
}

export const getMessageMedia = async <T = Blob>(
  projectId: string,
  chatId: string,
  mediaId: string,
  options: GetMessageMediaOptions = {},
): Promise<AxiosResponse<T>> => {
  const base = await baseServiceEndpoint();
  const { asJson = false } = options;

  return base.get<T>(
    `/user/project/${projectId}/chat/${chatId}/media/${mediaId}`,
    {
      responseType: asJson ? "json" : "blob",
      params: asJson ? { json: true } : undefined,
    },
  );
};
