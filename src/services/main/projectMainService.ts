import baseServiceEndpoint from "@/services/main/baseServiceEndpoint.ts";

export interface Project {
  uid: string;
  projectName: string;
  description: string;
  createdAt: string;
  updatedAt: string;
  whatsappCredentials: {
    phoneNumber: string;
    provider: string;
    bearerToken: string;
    whatsappBusinessAccountId: string;
    phoneNumberId: string;
    webhookVerificationToken: string;
  };
  ownerUserUid: string;
  userAccess: {
    uids: string[];
    users: {
      uid: string;
      email: string;
      role: string;
    }[];
  };
}

export const getProjects = async () => {
  const base = await baseServiceEndpoint();

  return base.get<{ data: Project[] }>("/user/project");
};

export const getProject = async (projectId: string) => {
  const base = await baseServiceEndpoint();

  return base.get<{ data: Project }>(`/user/project/${projectId}`);
};

export const updateProject = async (
  projectId: string,
  project: Pick<Project, "projectName" | "description">,
) => {
  const base = await baseServiceEndpoint();

  return base.put(`/user/project/${projectId}`, project);
};
