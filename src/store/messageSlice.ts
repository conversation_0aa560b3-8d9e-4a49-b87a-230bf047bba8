import type { MessageContentType, NormalizedMessage } from "@/types";
import type { ChatModel } from "@/types/firestore/chatModel";

import { createSlice, PayloadAction } from "@reduxjs/toolkit";

export type LoadState = "none" | "loading" | "loaded";

interface MessageState {
  messages: NormalizedMessage[];
  activeChat: ChatModel | null;
  loadState: LoadState;
}

const initialState: MessageState = {
  messages: [],
  activeChat: null,
  loadState: "loading",
};

export const messageSlice = createSlice({
  name: "message",
  initialState,
  reducers: {
    setActiveChat: (state, action: PayloadAction<ChatModel>) => {
      state.activeChat = action.payload;
      // Mark messages as read when chat is opened
      const messages = state.messages.filter(
        (message) => message.chatId === action.payload.id,
      );

      messages.forEach((message) => {
        if (message.sender.type === "contact" && message.status !== "read") {
          message.status = "read";
        }
      });
    },

    addMessage: (state, action: PayloadAction<NormalizedMessage>) => {
      const message = action.payload;

      state.messages.push(message);
    },

    sendMessage: (
      state,
      action: PayloadAction<{
        chatId: string;
        content: string;
        type: MessageContentType;
      }>,
    ) => {
      const { chatId, content, type } = action.payload;
      const message: NormalizedMessage = {
        id: crypto.randomUUID(),
        chatId,
        whatsappMessageId: `temp_${Date.now()}`,
        type,

        // Add type-specific content based on message type
        ...(type === "text" && { text: { body: content } }),

        sender: {
          id: "current-user",
          name: "You",
          phoneNumber: "",
          whatsappId: "",
          type: "user",
        },
        timestamp: new Date().toISOString(),
        status: "sending",

        // Required fields
        direction: "out",
        metadata: {},
      };

      state.messages.push(message);
    },

    updateMessageStatus: (
      state,
      action: PayloadAction<{
        messageId: string;
        status: "sent" | "delivered" | "read" | "failed";
      }>,
    ) => {
      const { messageId, status } = action.payload;

      const message = state.messages.find((m) => m.id === messageId);

      if (message) {
        message.status = status;
      }
    },

    setLoading: (state, action: PayloadAction<LoadState>) => {
      state.loadState = action.payload;
    },

    setMessages: (state, action: PayloadAction<NormalizedMessage[]>) => {
      state.messages = action.payload;
    },
  },
});

export const {
  setActiveChat,
  addMessage,
  sendMessage,
  updateMessageStatus,
  setLoading,
  setMessages,
} = messageSlice.actions;

export default messageSlice.reducer;
