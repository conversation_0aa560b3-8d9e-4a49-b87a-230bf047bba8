import { createSlice, PayloadAction } from "@reduxjs/toolkit";

import { Contact, ContactsState, ContactFormData } from "@/types/contact";

const initialState: ContactsState = {
  contacts: [],
  filteredContacts: [],
  searchQuery: "",
  selectedContact: null,
  isModalOpen: false,
  modalMode: "add",
  loading: false,
};

const contactsSlice = createSlice({
  name: "contacts",
  initialState,
  reducers: {
    setContacts: (state, action: PayloadAction<Contact[]>) => {
      state.contacts = action.payload;
      state.filteredContacts = action.payload;
    },
    addContact: (state, action: PayloadAction<ContactFormData>) => {
      const newContact: Contact = {
        id: Date.now().toString(),
        ...action.payload,
        avatar: action.payload.avatar
          ? URL.createObjectURL(action.payload.avatar)
          : undefined,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      state.contacts.push(newContact);
      state.filteredContacts = state.contacts.filter(
        (contact) =>
          contact.name
            .toLowerCase()
            .includes(state.searchQuery.toLowerCase()) ||
          contact.email
            .toLowerCase()
            .includes(state.searchQuery.toLowerCase()) ||
          contact.phone.includes(state.searchQuery),
      );
    },
    updateContact: (
      state,
      action: PayloadAction<{ id: string; data: ContactFormData }>,
    ) => {
      const { id, data } = action.payload;
      const index = state.contacts.findIndex((contact) => contact.id === id);

      if (index !== -1) {
        state.contacts[index] = {
          ...state.contacts[index],
          ...data,
          avatar: data.avatar
            ? URL.createObjectURL(data.avatar)
            : state.contacts[index].avatar,
          updatedAt: new Date().toISOString(),
        };
        state.filteredContacts = state.contacts.filter(
          (contact) =>
            contact.name
              .toLowerCase()
              .includes(state.searchQuery.toLowerCase()) ||
            contact.email
              .toLowerCase()
              .includes(state.searchQuery.toLowerCase()) ||
            contact.phone.includes(state.searchQuery),
        );
      }
    },
    deleteContact: (state, action: PayloadAction<string>) => {
      state.contacts = state.contacts.filter(
        (contact) => contact.id !== action.payload,
      );
      state.filteredContacts = state.contacts.filter(
        (contact) =>
          contact.name
            .toLowerCase()
            .includes(state.searchQuery.toLowerCase()) ||
          contact.email
            .toLowerCase()
            .includes(state.searchQuery.toLowerCase()) ||
          contact.phone.includes(state.searchQuery),
      );
    },
    setSearchQuery: (state, action: PayloadAction<string>) => {
      state.searchQuery = action.payload;
      state.filteredContacts = state.contacts.filter(
        (contact) =>
          contact.name.toLowerCase().includes(action.payload.toLowerCase()) ||
          contact.email.toLowerCase().includes(action.payload.toLowerCase()) ||
          contact.phone.includes(action.payload),
      );
    },
    setSelectedContact: (state, action: PayloadAction<Contact | null>) => {
      state.selectedContact = action.payload;
    },
    openModal: (
      state,
      action: PayloadAction<{
        mode: "add" | "edit" | "view";
        contact?: Contact;
      }>,
    ) => {
      state.isModalOpen = true;
      state.modalMode = action.payload.mode;
      state.selectedContact = action.payload.contact || null;
    },
    closeModal: (state) => {
      state.isModalOpen = false;
      state.selectedContact = null;
    },
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.loading = action.payload;
    },
  },
});

export const {
  setContacts,
  addContact,
  updateContact,
  deleteContact,
  setSearchQuery,
  setSelectedContact,
  openModal,
  closeModal,
  setLoading,
} = contactsSlice.actions;

export default contactsSlice.reducer;
