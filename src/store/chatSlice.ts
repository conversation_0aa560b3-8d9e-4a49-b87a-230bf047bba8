import { createSlice, PayloadAction } from "@reduxjs/toolkit";

import { ChatModel } from "@/types/firestore/chatModel";

interface ChatState {
  chats: ChatModel[];
  error: string | null;
  searchQuery: string;
}

const initialState: ChatState = {
  chats: [],
  error: null,
  searchQuery: "",
};

export const chatSlice = createSlice({
  name: "chat",
  initialState,
  reducers: {
    setChats: (state, action: PayloadAction<ChatModel[]>) => {
      state.chats = action.payload;
    },

    addChat: (state, action: PayloadAction<ChatModel>) => {
      const existingIndex = state.chats.findIndex(
        (c) => c.id === action.payload.id,
      );

      if (existingIndex !== -1) {
        state.chats[existingIndex] = action.payload;
      } else {
        state.chats.unshift(action.payload);
      }
    },

    setSearchQuery: (state, action: PayloadAction<string>) => {
      state.searchQuery = action.payload;
    },

    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
    },
  },
});

export const { setChats, addChat, setSearchQuery, setError } =
  chatSlice.actions;

export default chatSlice.reducer;
