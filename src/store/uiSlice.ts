import { createSlice, PayloadAction } from "@reduxjs/toolkit";

interface UIState {
  // Main menu state
  isMainMenuExpanded: boolean;

  // Mobile sidebar state
  isMobileSidebarOpen: boolean;

  // Panel visibility for responsive behavior
  visiblePanels: {
    mainMenu: boolean;
    leftPanel: boolean;
    centerPanel: boolean;
    rightPanel: boolean;
  };

  // Screen size tracking
  screenSize: "mobile" | "tablet" | "desktop";

  // Theme and appearance
  theme: "light" | "dark";

  // Loading states
  isInitializing: boolean;
}

const initialState: UIState = {
  isMainMenuExpanded: false,
  isMobileSidebarOpen: false,
  visiblePanels: {
    mainMenu: true,
    leftPanel: true,
    centerPanel: true,
    rightPanel: false, // Hidden by default on smaller screens
  },
  screenSize: "desktop",
  theme: "light",
  isInitializing: true,
};

export const uiSlice = createSlice({
  name: "ui",
  initialState,
  reducers: {
    toggleMainMenu: (state) => {
      state.isMainMenuExpanded = !state.isMainMenuExpanded;
    },

    setMainMenuExpanded: (state, action: PayloadAction<boolean>) => {
      state.isMainMenuExpanded = action.payload;
    },

    openMobileSidebar: (state) => {
      state.isMobileSidebarOpen = true;
      // On mobile, when sidebar opens, we want to show the main menu expanded
      if (state.screenSize === "mobile") {
        state.isMainMenuExpanded = true;
      }
    },

    closeMobileSidebar: (state) => {
      state.isMobileSidebarOpen = false;
      // On mobile, when sidebar closes, we want to hide the main menu
      if (state.screenSize === "mobile") {
        state.isMainMenuExpanded = false;
      }
    },

    toggleMobileSidebar: (state) => {
      state.isMobileSidebarOpen = !state.isMobileSidebarOpen;
      // On mobile, toggle the main menu expansion as well
      if (state.screenSize === "mobile") {
        state.isMainMenuExpanded = state.isMobileSidebarOpen;
      }
    },

    setPanelVisibility: (
      state,
      action: PayloadAction<{
        panel: keyof UIState["visiblePanels"];
        visible: boolean;
      }>,
    ) => {
      state.visiblePanels[action.payload.panel] = action.payload.visible;
    },

    setVisiblePanels: (
      state,
      action: PayloadAction<Partial<UIState["visiblePanels"]>>,
    ) => {
      state.visiblePanels = { ...state.visiblePanels, ...action.payload };
    },

    setScreenSize: (
      state,
      action: PayloadAction<"mobile" | "tablet" | "desktop">,
    ) => {
      state.screenSize = action.payload;

      // Auto-adjust panel visibility based on screen size
      switch (action.payload) {
        case "mobile":
          state.visiblePanels = {
            mainMenu: true, // Allow main menu on mobile but keep it collapsed by default
            leftPanel: true,
            centerPanel: false,
            rightPanel: false,
          };
          // On mobile, sync main menu expansion with mobile sidebar state
          state.isMainMenuExpanded = state.isMobileSidebarOpen;
          break;
        case "tablet":
          state.visiblePanels = {
            mainMenu: true,
            leftPanel: true,
            centerPanel: true,
            rightPanel: false,
          };
          state.isMainMenuExpanded = false;
          break;
        case "desktop":
          state.visiblePanels = {
            mainMenu: true,
            leftPanel: true,
            centerPanel: true,
            rightPanel: true,
          };
          // On desktop, we don't need to track mobile sidebar state
          state.isMobileSidebarOpen = false;
          break;
      }
    },

    showMobileChatView: (state) => {
      // For mobile: show only center panel (active chat) but keep main menu available
      state.visiblePanels = {
        mainMenu: true,
        leftPanel: false,
        centerPanel: true,
        rightPanel: false,
      };
      // When showing chat view, close the mobile sidebar
      if (state.screenSize === "mobile") {
        state.isMobileSidebarOpen = false;
        state.isMainMenuExpanded = false;
      }
    },

    showMobileChatListView: (state) => {
      // For mobile: show only left panel (chat list) but keep main menu available
      state.visiblePanels = {
        mainMenu: true,
        leftPanel: true,
        centerPanel: false,
        rightPanel: false,
      };
      // When showing chat list view, close the mobile sidebar
      if (state.screenSize === "mobile") {
        state.isMobileSidebarOpen = false;
        state.isMainMenuExpanded = false;
      }
    },

    toggleRightPanel: (state) => {
      state.visiblePanels.rightPanel = !state.visiblePanels.rightPanel;
    },

    openRightPanel: (state) => {
      // Open right panel when selecting a chat (desktop only)
      if (state.screenSize === "desktop") {
        state.visiblePanels.rightPanel = true;
      }
    },

    closeRightPanel: (state) => {
      state.visiblePanels.rightPanel = false;
    },

    toggleTheme: (state) => {
      state.theme = state.theme === "light" ? "dark" : "light";
    },

    setTheme: (state, action: PayloadAction<"light" | "dark">) => {
      state.theme = action.payload;
    },

    setInitializing: (state, action: PayloadAction<boolean>) => {
      state.isInitializing = action.payload;
    },
  },
});

export const {
  toggleMainMenu,
  setMainMenuExpanded,
  openMobileSidebar,
  closeMobileSidebar,
  toggleMobileSidebar,
  setPanelVisibility,
  setVisiblePanels,
  setScreenSize,
  showMobileChatView,
  showMobileChatListView,
  toggleRightPanel,
  openRightPanel,
  closeRightPanel,
  toggleTheme,
  setTheme,
  setInitializing,
} = uiSlice.actions;

export default uiSlice.reducer;
