import { createSlice, createAsyncThunk, PayloadAction } from "@reduxjs/toolkit";

import { LabelModel } from "@/types/firestore/labelModel";
import {
  getAllLabels,
  createLabel,
  updateLabel,
  deleteLabel,
} from "@/services/main/labelMainService";

// Define the state interface
interface LabelsState {
  labels: LabelModel[];
  loading: boolean;
  error: string | null;
}

// Initial state
const initialState: LabelsState = {
  labels: [],
  loading: false,
  error: null,
};

// Async thunks for API operations
export const fetchAllLabels = createAsyncThunk(
  "labels/fetchAllLabels",
  async ({ projectId }: { projectId: string }) => {
    const response = await getAllLabels(projectId);

    return response.data.data;
  },
);

export const createNewLabel = createAsyncThunk(
  "labels/createLabel",
  async ({
    projectId,
    labelData,
  }: {
    projectId: string;
    labelData: { name: string; description?: string };
  }) => {
    const response = await createLabel(projectId, labelData);

    return response.data.data;
  },
);

export const updateExistingLabel = createAsyncThunk(
  "labels/updateLabel",
  async ({
    projectId,
    labelId,
    labelData,
  }: {
    projectId: string;
    labelId: string;
    labelData: { name?: string; description?: string };
  }) => {
    const response = await updateLabel(projectId, labelId, labelData);

    return response.data.data;
  },
);

export const deleteExistingLabel = createAsyncThunk(
  "labels/deleteLabel",
  async ({ projectId, labelId }: { projectId: string; labelId: string }) => {
    await deleteLabel(projectId, labelId);

    return labelId;
  },
);

// Create the slice
export const labelsSlice = createSlice({
  name: "labels",
  initialState,
  reducers: {
    // Additional reducers if needed
    clearLabels: (state) => {
      state.labels = [];
    },
    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
    },
  },
  extraReducers: (builder) => {
    // Handle fetch all labels
    builder
      .addCase(fetchAllLabels.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(
        fetchAllLabels.fulfilled,
        (state, action: PayloadAction<LabelModel[]>) => {
          state.loading = false;
          state.labels = action.payload;
        },
      )
      .addCase(fetchAllLabels.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || "Failed to fetch labels";
      });

    // Handle create label
    builder
      .addCase(createNewLabel.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(
        createNewLabel.fulfilled,
        (state, action: PayloadAction<LabelModel>) => {
          state.loading = false;
          state.labels.push(action.payload);
        },
      )
      .addCase(createNewLabel.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || "Failed to create label";
      });

    // Handle update label
    builder
      .addCase(updateExistingLabel.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(
        updateExistingLabel.fulfilled,
        (state, action: PayloadAction<LabelModel>) => {
          state.loading = false;
          const index = state.labels.findIndex(
            (label) => label.id === action.payload.id,
          );

          if (index !== -1) {
            state.labels[index] = action.payload;
          }
        },
      )
      .addCase(updateExistingLabel.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || "Failed to update label";
      });

    // Handle delete label
    builder
      .addCase(deleteExistingLabel.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(
        deleteExistingLabel.fulfilled,
        (state, action: PayloadAction<string>) => {
          state.loading = false;
          state.labels = state.labels.filter(
            (label) => label.id !== action.payload,
          );
        },
      )
      .addCase(deleteExistingLabel.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || "Failed to delete label";
      });
  },
});

// Export actions
export const { clearLabels, setError } = labelsSlice.actions;

// Export the reducer
export default labelsSlice.reducer;
