import { Route, Routes } from "react-router-dom";

import { Provider } from "./provider";

import Dashboard from "@/pages/dashboard/Dashboard.tsx";
import Chats from "@/pages/chats/Chats.tsx";
import Login from "@/pages/login/Login.tsx";
import SignUp from "@/pages/signup/SignUp.tsx";
import Contacts from "@/pages/contacts/Contacts.tsx";
import SelectProject from "@/pages/select-project/SelectProject.tsx";
import AccountSettings from "@/pages/settings/AccountSettings.tsx";
import ProjectSettings from "@/pages/settings/ProjectSettings.tsx";
import Labels from "@/pages/labels/Labels.tsx";
import ProtectedRoute from "@/components/route/ProtectedRoute.tsx";
import RedirectIfLoggedInRoute from "@/components/route/RedirectIfLoggedInRoute.tsx";
import ProtectedProjectRoute from "@/components/route/ProtectedProjectRoute.tsx";

function App() {
  return (
    <Provider>
      <Routes>
        <Route element={<RedirectIfLoggedInRoute />}>
          <Route element={<Login />} path="/login" />
          <Route element={<SignUp />} path="/signup" />
        </Route>

        <Route element={<ProtectedRoute />}>
          <Route element={<ProtectedProjectRoute />} path="/project/:projectId">
            <Route element={<Dashboard />} path="dashboard" />
            <Route element={<Chats />} path="chats" />
            <Route element={<ProjectSettings />} path="settings" />
            <Route element={<Contacts />} path="contacts" />
            <Route element={<Labels />} path="labels" />
          </Route>

          <Route element={<SelectProject />} path="/" />
          <Route element={<AccountSettings />} path="/settings/account" />
        </Route>
      </Routes>
    </Provider>
  );
}

export default App;
