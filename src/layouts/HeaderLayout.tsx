/**
 * HeaderLayout Component
 *
 * A simplified layout component that only includes the application name and account section.
 * Designed for use on pages like SelectProject where a minimal top header is appropriate.
 *
 * Features:
 * - Application name/logo
 * - User profile/account section
 * - Responsive design with mobile-first approach
 * - Dark mode support
 *
 * @example
 * ```tsx
 * <HeaderLayout>
 *   <ContentArea />
 * </HeaderLayout>
 * ```
 */

import type { ReactNode } from "react";

import { useEffect, useRef, useState } from "react";
import { useNavigate } from "react-router-dom";
import { LogOut, UserCog } from "lucide-react";

import { useAuth } from "@/components/providers/AuthProvider.tsx";
import { getInitials } from "@/utils/stringUtils.ts";

// Props interface for the HeaderLayout component
interface HeaderLayoutProps {
  children: ReactNode;
}

// Main HeaderLayout component
export default function HeaderLayout({ children }: HeaderLayoutProps) {
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const navigate = useNavigate();
  const auth = useAuth();

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setIsDropdownOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);

    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  return (
    <div className="relative min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Header/Navigation Bar */}
      <header className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 shadow-sm">
        <div className="px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            {/* Left side - Logo */}
            <div className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
                <span className="text-white font-bold text-sm">IL</span>
              </div>
              <span className="font-semibold text-gray-900 dark:text-white hidden sm:block">
                Ideal Lumatera
              </span>
            </div>

            {/* Right side - User Profile */}
            <div className="relative flex items-center">
              <button
                className="flex items-center space-x-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-md px-2 py-1 transition-colors cursor-pointer"
                onClick={() => setIsDropdownOpen(!isDropdownOpen)}
              >
                <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                  <span className="text-white font-medium text-xs">
                    {auth.user?.name ? getInitials(auth.user.name) : ""}
                  </span>
                </div>
                <span className="text-gray-900 dark:text-white hidden sm:block">
                  {auth.user?.name}
                </span>
                <span className="text-gray-900 dark:text-white">▼</span>
              </button>
              {isDropdownOpen && (
                <div
                  ref={dropdownRef}
                  className="absolute right-0 top-full mt-2 w-48 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-md shadow-lg z-10"
                  role="menu"
                >
                  <div className="px-4 py-2 border-b border-gray-200 dark:border-gray-600">
                    <div className="text-sm font-medium text-gray-900 dark:text-white">
                      {auth.user?.name}
                    </div>
                    <div className="text-xs text-gray-500 dark:text-gray-400">
                      {auth.user?.email}
                    </div>
                  </div>
                  <button
                    className="flex w-full items-center space-x-2 px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 dark:text-gray-200 dark:hover:bg-gray-700 transition-colors"
                    role="menuitem"
                    onClick={() => {
                      setIsDropdownOpen(false);
                      navigate("/settings/account");
                    }}
                  >
                    <UserCog className="h-4 w-4" />
                    <span>Settings</span>
                  </button>
                  <button
                    className="flex w-full items-center space-x-2 px-3 py-2 text-sm text-red-600 hover:bg-red-50 dark:text-red-400 dark:hover:bg-red-900/30 transition-colors"
                    role="menuitem"
                    onClick={async () => {
                      await auth.logout();
                      setIsDropdownOpen(false);
                      navigate("/login");
                    }}
                  >
                    <LogOut className="h-4 w-4" />
                    <span>Logout</span>
                  </button>
                </div>
              )}
            </div>
          </div>
        </div>
      </header>

      {/* Main Content Area */}
      <main className="flex-1">{children}</main>
    </div>
  );
}

// Named export for the props interface
export type { HeaderLayoutProps };
