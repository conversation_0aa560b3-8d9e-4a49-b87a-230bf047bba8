import React from "react";
import { Button } from "@heroui/react";

import { useModal } from "@/hooks/useModal";

const ModalExample: React.FC = () => {
  const { showConfirm, showError, showSuccess, showInfo } = useModal();

  const handleConfirm = () => {
    showConfirm({
      title: "Konfirmasi",
      message: "Apakah Anda yakin ingin melanjutkan?",
      onConfirm: () => {
        // User confirmed
      },
      onCancel: () => {
        // User cancelled
      },
      confirmText: "Ya",
      cancelText: "Tidak",
    });
  };

  const handleError = () => {
    showError({
      title: "Error",
      message: "<PERSON><PERSON><PERSON><PERSON> kes<PERSON>han, silakan coba lagi.",
      onConfirm: () => {
        // Error acknowledged
      },
      confirmText: "OK",
    });
  };

  const handleSuccess = () => {
    showSuccess({
      title: "<PERSON>r<PERSON><PERSON>",
      message: "Operasi berhasil dilakukan.",
      onConfirm: () => {
        // Success acknowledged
      },
      confirmText: "OK",
    });
  };

  const handleInfo = () => {
    showInfo({
      title: "Informasi",
      message: "Ini adalah pesan informasi untuk pengguna.",
      onConfirm: () => {
        // Info acknowledged
      },
      confirmText: "OK",
    });
  };

  const handleAutoClose = () => {
    showSuccess({
      title: "Auto Close",
      message: "Modal ini akan otomatis tertutup dalam 3 detik.",
      autoCloseTimer: 3000,
      onConfirm: () => {
        // Auto close acknowledged
      },
    });
  };

  return (
    <div className="p-4">
      <h2 className="text-xl font-bold mb-4">Contoh Penggunaan Modal</h2>
      <div className="flex flex-wrap gap-4">
        <Button color="primary" onPress={handleConfirm}>
          Tampilkan Modal Konfirmasi
        </Button>
        <Button color="danger" onPress={handleError}>
          Tampilkan Modal Error
        </Button>
        <Button color="success" onPress={handleSuccess}>
          Tampilkan Modal Sukses
        </Button>
        <Button color="primary" variant="bordered" onPress={handleInfo}>
          Tampilkan Modal Info
        </Button>
        <Button color="warning" onPress={handleAutoClose}>
          Tampilkan Modal Auto Close
        </Button>
      </div>
    </div>
  );
};

export default ModalExample;
