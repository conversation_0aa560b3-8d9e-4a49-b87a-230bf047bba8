import React, { create<PERSON>ontext, ReactNode, useEffect, useState } from "react";
import {
  <PERSON>ton,
  <PERSON>dal,
  <PERSON>dal<PERSON><PERSON>,
  <PERSON>dal<PERSON>ontent,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON><PERSON><PERSON>eader,
} from "@heroui/react";
import {
  CheckCircleIcon,
  ExclamationTriangleIcon,
  InformationCircleIcon,
  XCircleIcon,
} from "@heroicons/react/24/solid";

import {
  ConfirmModalConfig,
  ErrorModalConfig,
  InfoModalConfig,
  ModalContextType,
  ModalState,
  SuccessModalConfig,
} from "@/types/modal";

const initialState: ModalState = {
  type: null,
  config: null,
  isOpen: false,
};

export const ModalContext = createContext<ModalContextType | undefined>(
  undefined,
);

interface ModalProviderProps {
  children: ReactNode;
}

const ModalProvider: React.FC<ModalProviderProps> = ({ children }) => {
  const [state, setState] = useState<ModalState>(initialState);
  const [autoCloseTimer, setAutoCloseTimer] = useState<NodeJS.Timeout | null>(
    null,
  );

  // Clear auto-close timer when modal closes
  useEffect(() => {
    return () => {
      if (autoCloseTimer) {
        clearTimeout(autoCloseTimer);
        setAutoCloseTimer(null);
      }
    };
  }, [autoCloseTimer]);

  const closeModal = () => {
    if (autoCloseTimer) {
      clearTimeout(autoCloseTimer);
      setAutoCloseTimer(null);
    }

    setState(initialState);
  };

  const showConfirm = (config: ConfirmModalConfig) => {
    setState({
      type: "confirm",
      config: {
        ...config,
        confirmText: config.confirmText || "OK",
        cancelText: config.cancelText || "Cancel",
      },
      isOpen: true,
    });
  };

  const showError = (config: ErrorModalConfig) => {
    setState({
      type: "error",
      config: {
        ...config,
        confirmText: config.confirmText || "OK",
      },
      isOpen: true,
    });
  };

  const showSuccess = (config: SuccessModalConfig) => {
    setState({
      type: "success",
      config: {
        ...config,
        confirmText: config.confirmText || "OK",
      },
      isOpen: true,
    });
  };

  const showInfo = (config: InfoModalConfig) => {
    setState({
      type: "info",
      config: {
        ...config,
        confirmText: config.confirmText || "OK",
      },
      isOpen: true,
    });
  };

  const handleConfirm = () => {
    if (
      state.config &&
      "onConfirm" in state.config &&
      typeof state.config.onConfirm === "function"
    ) {
      state.config.onConfirm();
    }
    closeModal();
  };

  const handleCancel = () => {
    if (
      state.config &&
      "onCancel" in state.config &&
      typeof state.config.onCancel === "function"
    ) {
      state.config.onCancel();
    }
    closeModal();
  };

  const handleClose = () => {
    if (state.config && state.config.onClose) {
      state.config.onClose();
    }
    closeModal();
  };

  // Handle auto-close timer
  useEffect(() => {
    if (
      state.isOpen &&
      state.config &&
      state.config.autoCloseTimer &&
      state.config.autoCloseTimer > 0
    ) {
      const timer = setTimeout(() => {
        closeModal();
      }, state.config.autoCloseTimer);

      setAutoCloseTimer(timer);
    } else if (autoCloseTimer) {
      clearTimeout(autoCloseTimer);
      setAutoCloseTimer(null);
    }
  }, [state.isOpen, state.config]);

  // Determine modal styling and icon based on type
  const getModalConfig = () => {
    switch (state.type) {
      case "success":
        return {
          icon: <CheckCircleIcon className="w-6 h-6 text-success" />,
          color: "success" as const,
        };
      case "error":
        return {
          icon: <XCircleIcon className="w-6 h-6 text-danger" />,
          color: "danger" as const,
        };
      case "confirm":
        return {
          icon: <ExclamationTriangleIcon className="w-6 h-6 text-warning" />,
          color: "warning" as const,
        };
      default: // info/default case
        return {
          icon: <InformationCircleIcon className="w-6 h-6 text-primary" />,
          color: "primary" as const,
        };
    }
  };

  const { icon, color } = getModalConfig();

  return (
    <ModalContext.Provider
      value={{
        state,
        showConfirm,
        showError,
        showSuccess,
        showInfo,
        closeModal,
      }}
    >
      {children}
      <Modal
        backdrop="blur"
        isOpen={state.isOpen}
        motionProps={{
          variants: {
            enter: {
              y: 0,
              opacity: 1,
              transition: {
                duration: 0.2,
                ease: "easeOut",
              },
            },
            exit: {
              y: 10,
              opacity: 0,
              transition: {
                duration: 0.15,
                ease: "easeIn",
              },
            },
          },
        }}
        scrollBehavior="inside"
        size="md"
        onClose={handleClose}
      >
        <ModalContent>
          <ModalHeader className="flex items-center gap-3">
            <div className="p-1 rounded-full bg-default-100">{icon}</div>
            <span className="text-xl font-semibold">{state.config?.title}</span>
          </ModalHeader>
          <ModalBody>
            <p>{state.config?.message}</p>
          </ModalBody>
          <ModalFooter>
            <div className="flex justify-end gap-2 w-full">
              {state.type === "confirm" && (
                <Button color="default" variant="light" onPress={handleCancel}>
                  {(state.config as ConfirmModalConfig)?.cancelText}
                </Button>
              )}
              <Button color={color} onPress={handleConfirm}>
                {(state.config as ConfirmModalConfig)?.confirmText ||
                  (state.config as ErrorModalConfig)?.confirmText ||
                  (state.config as SuccessModalConfig)?.confirmText ||
                  "OK"}
              </Button>
            </div>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </ModalContext.Provider>
  );
};

export default ModalProvider;
