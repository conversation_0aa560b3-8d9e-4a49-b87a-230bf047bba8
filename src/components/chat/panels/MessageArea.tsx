import type { NormalizedMessage } from "@/types";

import React, { useEffect, useRef, useState } from "react";
import { useParams } from "react-router-dom";
import dayjs from "dayjs";
import {
  IoAlertCircleOutline,
  IoChatbubbleEllipsesOutline,
  IoReload,
  IoSyncOutline,
} from "react-icons/io5";

import MessageBubble from "./MessageBubble";

import { useAppDispatch, useAppSelector } from "@/store/hooks.ts";
import { setLoading, setMessages } from "@/store/messageSlice.ts";
import subscribeToMessages from "@/services/firebase/messageService.ts";

interface MessageAreaProps {
  chatId: string;
}

const MessageArea: React.FC<MessageAreaProps> = ({ chatId }) => {
  const bottomRef = useRef<HTMLDivElement>(null);
  const dispatch = useAppDispatch();
  const { projectId } = useParams();
  const { messages, loadState, activeChat } = useAppSelector(
    (state) => state.message,
  );
  const [connectionError, setConnectionError] = useState<string | null>(null);

  const chatMessages = messages;

  // Setup realtime message listener
  useEffect(() => {
    if (!projectId || !chatId) return;

    let unsubscribe: (() => void) | null = null;

    dispatch(setLoading("loading"));
    setConnectionError(null);

    const setupListener = () => {
      unsubscribe = subscribeToMessages(
        projectId,
        chatId,
        (newMessages) => {
          // Filter out existing messages for this chatId and add new ones
          const otherChatMessages = messages.filter(
            (message) => message.chatId !== chatId,
          );
          const updatedMessages = [...otherChatMessages, ...newMessages];

          dispatch(setMessages(updatedMessages));
          dispatch(setLoading("loaded"));
        },
        (_error) => {
          // Log error for debugging
          // In production, consider using a proper error tracking service
          setConnectionError(
            "Failed to connect to messages. Please check your connection.",
          );
          dispatch(setLoading("none"));
        },
      );
    };

    setupListener();

    // Cleanup listener on component unmount
    return () => {
      if (unsubscribe) {
        unsubscribe();
      }
    };
  }, [projectId, chatId, dispatch]);

  useEffect(() => {
    const timeout = setTimeout(() => {
      if (loadState === "loaded") {
        bottomRef.current?.scrollIntoView({
          behavior: "instant",
        });
      }
    }, 50);

    return () => {
      clearTimeout(timeout);
    };
  }, [loadState]);

  // Define the type for grouped messages
  type GroupedMessagesType = Record<
    string,
    Record<string, NormalizedMessage[]>
  >;

  // Group messages by minute for the same sender
  const groupedMessages = chatMessages.reduce((groups, message) => {
    const dateKey = dayjs(message.timestamp).format("YYYY-MM-DD");
    const minuteKey = dayjs(message.timestamp).format("YYYY-MM-DD HH:mm");
    const groupKey = `${minuteKey}_${message.sender.id}`;

    if (!groups[dateKey]) {
      groups[dateKey] = {};
    }

    if (!groups[dateKey][groupKey]) {
      groups[dateKey][groupKey] = [];
    }
    groups[dateKey][groupKey].push(message);

    return groups;
  }, {} as GroupedMessagesType);

  const formatDateHeader = (dateString: string): string => {
    const date = dayjs(dateString);
    const today = dayjs();
    const yesterday = today.subtract(1, "day");

    if (date.isSame(today, "day")) {
      return "Today";
    } else if (date.isSame(yesterday, "day")) {
      return "Yesterday";
    } else {
      return date.format("MMMM D, YYYY");
    }
  };

  if (loadState === "loading") {
    return (
      <div className="flex items-center justify-center h-full bg-white dark:bg-gray-800">
        <div className="text-center">
          <div className="w-16 h-16 mx-auto mb-4 bg-blue-500 rounded-full flex items-center justify-center">
            <IoSyncOutline className="animate-spin h-8 w-8 text-white" />
          </div>
          <p className="text-gray-500 dark:text-gray-400">
            Loading messages...
          </p>
        </div>
      </div>
    );
  }

  if (connectionError) {
    return (
      <div className="flex items-center justify-center h-full bg-white dark:bg-gray-800">
        <div className="text-center">
          <div className="w-16 h-16 mx-auto mb-4 bg-red-500 rounded-full flex items-center justify-center">
            <IoAlertCircleOutline className="w-8 h-8 text-white" />
          </div>
          <p className="text-red-500 dark:text-red-400 mb-2">
            {connectionError}
          </p>
          <button
            className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors flex items-center space-x-2 mx-auto"
            onClick={() => window.location.reload()}
          >
            <IoReload className="w-4 h-4" />
            <span>Retry</span>
          </button>
        </div>
      </div>
    );
  }

  if (!activeChat) {
    return (
      <div className="flex items-center justify-center h-full bg-white dark:bg-gray-800">
        <div className="text-center">
          <p className="text-gray-500 dark:text-gray-400">No chat selected</p>
        </div>
      </div>
    );
  }

  if (chatMessages.length === 0) {
    return (
      <div className="flex items-center justify-center h-full bg-white dark:bg-gray-800">
        <div className="text-center">
          <div className="w-16 h-16 mx-auto mb-4 bg-blue-500 rounded-full flex items-center justify-center">
            <IoChatbubbleEllipsesOutline className="w-8 h-8 text-white" />
          </div>
          <p className="text-gray-500 dark:text-gray-400">
            No messages yet. Start the conversation!
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full overflow-y-auto p-4 space-y-4 bg-gray-50 dark:bg-gray-900 shadow-inner dark:shadow-black/30">
      {Object.entries(groupedMessages).map(([dateKey, groupsForDate]) => (
        <div key={dateKey}>
          {/* Date Separator */}
          <div className="flex items-center justify-center mb-4">
            <div className="bg-white dark:bg-gray-800 px-3 py-1 rounded-full shadow-sm border border-gray-200 dark:border-gray-700">
              <span className="text-xs font-medium text-gray-500 dark:text-gray-300">
                {formatDateHeader(dateKey)}
              </span>
            </div>
          </div>

          {/* Messages for this date */}
          <div className="space-y-2">
            {Object.values(groupsForDate).map((messageGroup) => {
              return messageGroup.map((message, index) => {
                const isFirstInGroup = index === 0;
                const isLastInGroup = index === messageGroup.length - 1;

                return (
                  <MessageBubble
                    key={message.id}
                    activeChat={activeChat}
                    isFirstInGroup={isFirstInGroup}
                    isLastInGroup={isLastInGroup}
                    message={message}
                  />
                );
              });
            })}
          </div>
        </div>
      ))}

      {/* Scroll */}
      <div ref={bottomRef} />
    </div>
  );
};

export default MessageArea;
