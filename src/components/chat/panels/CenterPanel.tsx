import type { CenterPanelProps } from "@/types";

import React from "react";
import { useOutletContext } from "react-router-dom";
import {
  IoChatbubbleEllipsesOutline,
  IoCloudOutline,
  IoLogoWhatsapp,
} from "react-icons/io5";

import ChatHeader from "./ChatHeader";
import MessageArea from "./MessageArea";
import MessageInput from "./MessageInput";

import { showMobileChatListView } from "@/store/uiSlice.ts";
import { useAppDispatch, useAppSelector } from "@/store/hooks.ts";
import { Project } from "@/services/main/projectMainService.ts";

const CenterPanel: React.FC<CenterPanelProps> = ({ className }) => {
  const dispatch = useAppDispatch();
  const { activeChat } = useAppSelector((state) => state.message);
  const { screenSize } = useAppSelector((state) => state.ui);
  const { project } = useOutletContext<{ project?: Project }>();
  const projectId = project?.uid;

  // Show welcome screen when no chat is selected
  if (!activeChat) {
    return (
      <div
        className={`flex items-center justify-center h-full bg-gray-50 dark:bg-gray-900 ${className}`}
      >
        <div className="text-center max-w-md mx-auto p-8">
          <div className="w-24 h-24 mx-auto mb-6 bg-blue-500 rounded-full flex items-center justify-center shadow-lg">
            <IoChatbubbleEllipsesOutline className="w-12 h-12 text-white" />
          </div>

          <h2 className="text-2xl font-semibold text-gray-900 dark:text-white mb-3">
            Welcome to Ideal Lumatera Chat
          </h2>

          <p className="text-gray-600 dark:text-gray-400 mb-6">
            Select a chat from the sidebar to start messaging with your
            contacts.
          </p>

          <div className="space-y-3 text-sm text-gray-500 dark:text-gray-400">
            <div className="flex items-center justify-center space-x-2">
              <IoCloudOutline className="w-4 h-4" />
              <span>Cloud-based messaging</span>
            </div>

            <div className="flex items-center justify-center space-x-2">
              <IoLogoWhatsapp className="w-4 h-4" />
              <span>WhatsApp Business API</span>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`flex flex-col h-full ${className}`}>
      {/* Chat Header */}
      <ChatHeader
        chat={activeChat}
        onBackClick={
          screenSize === "mobile"
            ? () => dispatch(showMobileChatListView())
            : undefined
        }
      />

      {/* Message Area */}
      <div className="flex-1 overflow-hidden bg-gray-100 dark:bg-gray-950">
        <MessageArea chatId={activeChat.id} />
      </div>

      {/* Message Input */}
      {projectId && (
        <MessageInput chatId={activeChat.id} projectId={projectId} />
      )}
    </div>
  );
};

export default CenterPanel;
