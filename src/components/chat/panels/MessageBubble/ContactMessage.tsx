import type { NormalizedMessage } from "../../../../types";

import React from "react";
import { RiUser3Line } from "react-icons/ri";

interface ContactMessageProps {
  message: NormalizedMessage;
}

const ContactMessage: React.FC<ContactMessageProps> = ({ message }) => {
  const contactContent = message.contact;
  const content = contactContent?.name.formattedName || "Contact";

  return (
    <div className="flex items-center space-x-2">
      <RiUser3Line className="h-5 w-5 text-gray-600 dark:text-gray-300" />
      <div>
        <p className="font-medium">Contact</p>
        <p className="text-sm opacity-75">{content}</p>
      </div>
    </div>
  );
};

ContactMessage.displayName = "ContactMessage";

export default ContactMessage;
