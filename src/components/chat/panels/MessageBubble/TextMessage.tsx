import type { NormalizedMessage } from "../../../../types";

import React from "react";

interface TextMessageProps {
  message: NormalizedMessage;
}

const TextMessage: React.FC<TextMessageProps> = ({ message }) => {
  const getContent = (): string => {
    switch (message.type) {
      case "text":
        return message.text?.body || "";
      case "reaction":
        return message.reaction?.emoji || "";
      case "button_reply":
        return message.interactive?.buttonReply?.text || "";
      case "list_reply":
        return message.interactive?.listReply?.title || "";
      case "order":
        return message.order?.text || "Order";
      case "system":
        return "System message";
      default:
        return "";
    }
  };

  const content = getContent();

  return <div className="break-words whitespace-pre-wrap">{content}</div>;
};

TextMessage.displayName = "TextMessage";

export default TextMessage;
