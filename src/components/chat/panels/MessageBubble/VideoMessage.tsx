import type { NormalizedMessage } from "../../../../types";

import React, { useEffect, useState } from "react";
import { RiLoader4Line, RiPlayCircleLine } from "react-icons/ri";
import { Modal, ModalBody, ModalContent, useDisclosure } from "@heroui/react";

import { getMessageMedia } from "@/services/main/messageMainService";

interface VideoMessageProps {
  message: NormalizedMessage;
}

const VideoMessage: React.FC<VideoMessageProps> = ({ message }) => {
  const [videoUrl, setVideoUrl] = useState<string | null>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const { isOpen, onOpen, onOpenChange } = useDisclosure();

  const caption = message.video?.caption;
  const mediaId = message.video?.id;

  useEffect(() => {
    if (!mediaId) {
      return undefined;
    }

    let isMounted = true;
    let objectUrl: string | null = null;

    const fetchVideo = async () => {
      setLoading(true);
      setError(null);

      try {
        const urlParts = window.location.pathname.split("/");
        const projectIdIndex = urlParts.indexOf("project");
        const projectId =
          projectIdIndex !== -1 && projectIdIndex + 1 < urlParts.length
            ? urlParts[projectIdIndex + 1]
            : null;

        if (!projectId) {
          if (isMounted) {
            setError("Project ID not found");
            setLoading(false);
          }

          return;
        }

        const response = await getMessageMedia(
          projectId,
          message.chatId,
          mediaId,
        );

        const blob = response.data;

        objectUrl = URL.createObjectURL(blob);

        if (isMounted) {
          setVideoUrl(objectUrl);
        }
      } catch {
        // Log error for debugging
        // In production, consider using a proper error tracking service

        if (isMounted) {
          setError("Failed to load video");
        }
      } finally {
        if (isMounted) {
          setLoading(false);
        }
      }
    };

    void fetchVideo();

    return () => {
      isMounted = false;

      if (objectUrl) {
        URL.revokeObjectURL(objectUrl);
      }
    };
  }, [mediaId, message.chatId]);

  const handleVideoClick = () => {
    if (videoUrl) {
      onOpen();
    }
  };

  if (loading) {
    return (
      <div className="flex flex-col space-y-3">
        <div className="relative bg-gray-200 dark:bg-gray-700 rounded-lg max-w-xs overflow-hidden">
          <div className="w-full h-40 sm:h-44 md:h-48 lg:h-80 rounded-lg flex flex-col items-center justify-center p-4 space-y-3">
            <div className="animate-spin text-blue-500 dark:text-blue-400">
              <RiLoader4Line className="h-8 w-8" />
            </div>
            <div className="text-gray-600 dark:text-gray-300 text-sm font-medium text-center">
              Loading video...
            </div>
          </div>
          {caption && (
            <div className="px-3 py-2 bg-gray-100 dark:bg-gray-800 border-t border-gray-300 dark:border-gray-600">
              <div className="text-sm opacity-80 break-words whitespace-pre-wrap text-gray-700 dark:text-gray-300">
                {caption}
              </div>
            </div>
          )}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex flex-col space-y-2">
        <div className="relative bg-red-10 dark:bg-red-900 rounded-lg max-w-xs h-40 sm:h-44 md:h-48 lg:h-80 flex items-center justify-center">
          <div className="flex items-center space-x-2">
            <RiPlayCircleLine className="h-5 w-5 text-red-600 dark:text-red-300" />
            <span className="text-sm font-medium text-red-700 dark:text-red-200">
              {error}
            </span>
          </div>
          {caption && (
            <div className="text-sm opacity-75 break-words whitespace-pre-wrap">
              {caption}
            </div>
          )}
        </div>
      </div>
    );
  }

  return (
    <>
      <div className="flex flex-col space-y-2">
        {videoUrl ? (
          <div
            aria-label={`View ${caption || "video"} in full size`}
            className="relative max-w-xs cursor-pointer"
            role="button"
            tabIndex={0}
            onClick={handleVideoClick}
            onKeyDown={(e) => {
              if (e.key === "Enter" || e.key === " ") {
                handleVideoClick();
              }
            }}
          >
            <video
              className="max-w-full h-40 sm:h-44 md:h-48 lg:h-80 object-cover rounded-lg shadow-sm"
              preload="metadata"
              src={videoUrl}
            >
              <track kind="captions" />
              Your browser does not support the video element.
            </video>
            <div className="absolute inset-0 flex items-center justify-center bg-transparent hover:bg-black/20 transition-all rounded-lg">
              <RiPlayCircleLine className="h-10 w-10 text-white opacity-70" />
            </div>
          </div>
        ) : (
          <div className="flex items-center space-x-2 p-2 bg-gray-100 dark:bg-gray-700 rounded-lg max-w-xs">
            <RiPlayCircleLine className="h-5 w-5 text-gray-600 dark:text-gray-300" />
            <span className="text-sm font-medium text-gray-700 dark:text-gray-200">
              Video
            </span>
          </div>
        )}

        {caption && (
          <div className="text-sm opacity-75 break-words whitespace-pre-wrap">
            {caption}
          </div>
        )}
      </div>

      <Modal
        backdrop="blur"
        classNames={{
          base: "bg-transparent",
          body: "p-0",
        }}
        isOpen={isOpen}
        size="full"
        onOpenChange={onOpenChange}
      >
        <ModalContent>
          {(close) => (
            <>
              <ModalBody>
                {videoUrl && (
                  <>
                    <div
                      className="flex h-full w-full items-center justify-center cursor-pointer"
                      role="button"
                      tabIndex={0}
                      onClick={close}
                      onKeyDown={(e) => {
                        if (e.key === "Enter" || e.key === " ") {
                          close();
                        }
                      }}
                    >
                      <video
                        controls
                        className="max-h-[90vh] max-w-full object-contain"
                        src={videoUrl}
                      >
                        <track kind="captions" />
                        Your browser does not support the video element.
                      </video>
                    </div>

                    {caption && (
                      <div className="absolute bottom-6 left-1/2 z-10 max-w-[90vw] -translate-x-1/2 rounded-lg bg-black/70 px-4 py-2 text-center text-sm text-white shadow-lg">
                        {caption}
                      </div>
                    )}
                  </>
                )}
              </ModalBody>
            </>
          )}
        </ModalContent>
      </Modal>
    </>
  );
};

VideoMessage.displayName = "VideoMessage";

export default VideoMessage;
