import type { NormalizedMessage } from "../../../../types";

import React, { useEffect, useState } from "react";
import { RiFile3Line } from "react-icons/ri";

import { getMessageMedia } from "@/services/main/messageMainService";

interface DocumentMessageProps {
  message: NormalizedMessage;
}

const DocumentMessage: React.FC<DocumentMessageProps> = ({ message }) => {
  const [documentUrl, setDocumentUrl] = useState<string | null>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  const caption = message.document?.caption;
  const mediaId = message.document?.id;
  const filename = message.document?.filename || "Document";

  useEffect(() => {
    if (!mediaId) {
      return;
    }

    const fetchDocument = async () => {
      setLoading(true);
      setError(null);

      try {
        // Extract projectId from the URL
        const urlParts = window.location.pathname.split("/");
        const projectIdIndex = urlParts.indexOf("project");
        const projectId =
          projectIdIndex !== -1 && projectIdIndex + 1 < urlParts.length
            ? urlParts[projectIdIndex + 1]
            : null;

        if (!projectId) {
          setError("Project ID not found");
          setLoading(false);

          return;
        }

        const response = await getMessageMedia(
          projectId,
          message.chatId,
          mediaId,
        );

        const blob = response.data;
        const url = URL.createObjectURL(blob);

        setDocumentUrl(url);
      } catch {
        // Log error for debugging
        // In production, consider using a proper error tracking service
        setError("Failed to load document");
      } finally {
        setLoading(false);
      }
    };

    fetchDocument();

    // Clean up the URL object when the component unmounts
    return () => {
      if (documentUrl) {
        URL.revokeObjectURL(documentUrl);
      }
    };
  }, [mediaId, message.chatId]);

  if (loading) {
    return (
      <div className="flex flex-col space-y-2">
        <div className="flex items-center space-x-2 p-2 bg-gray-100 dark:bg-gray-700 rounded-lg animate-pulse">
          <RiFile3Line className="h-5 w-5 text-gray-600 dark:text-gray-300" />
          <div className="flex-1 min-w-0">
            <div className="text-gray-500 dark:text-gray-300">
              Loading document...
            </div>
          </div>
        </div>
        {caption && (
          <div className="text-sm opacity-75 break-words whitespace-pre-wrap">
            {caption}
          </div>
        )}
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex flex-col space-y-2">
        <div className="flex items-center space-x-2 p-2 bg-red-100 dark:bg-red-900 rounded-lg">
          <RiFile3Line className="h-5 w-5 text-red-600 dark:text-red-300" />
          <span className="text-sm font-medium text-red-700 dark:text-red-200">
            {error}
          </span>
        </div>
        {caption && (
          <div className="text-sm opacity-75 break-words whitespace-pre-wrap">
            {caption}
          </div>
        )}
      </div>
    );
  }

  return (
    <div className="flex flex-col space-y-2">
      {documentUrl ? (
        <div className="flex items-center space-x-2 p-2 bg-gray-100 dark:bg-gray-700 rounded-lg">
          <RiFile3Line className="h-5 w-5 text-gray-600 dark:text-gray-300" />
          <div className="flex-1 min-w-0">
            <a
              className="text-sm font-medium text-blue-600 dark:text-blue-400 hover:underline truncate"
              download={filename}
              href={documentUrl}
              rel="noopener noreferrer"
              target="_blank"
            >
              {filename}
            </a>
          </div>
        </div>
      ) : (
        <div className="flex items-center space-x-2 p-2 bg-gray-100 dark:bg-gray-700 rounded-lg">
          <RiFile3Line className="h-5 w-5 text-gray-600 dark:text-gray-300" />
          <span className="text-sm font-medium text-gray-700 dark:text-gray-200">
            Document
          </span>
        </div>
      )}

      {caption && (
        <div className="text-sm opacity-75 break-words whitespace-pre-wrap">
          {caption}
        </div>
      )}
    </div>
  );
};

DocumentMessage.displayName = "DocumentMessage";

export default DocumentMessage;
