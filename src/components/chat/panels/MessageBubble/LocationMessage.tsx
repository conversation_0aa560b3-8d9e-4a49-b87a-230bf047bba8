import type { NormalizedMessage } from "../../../../types";

import React from "react";
import { RiMapPinLine } from "react-icons/ri";

interface LocationMessageProps {
  message: NormalizedMessage;
}

const LocationMessage: React.FC<LocationMessageProps> = ({ message }) => {
  const locationContent = message.location;
  const content =
    locationContent?.name ||
    locationContent?.address ||
    `${locationContent?.latitude}, ${locationContent?.longitude}` ||
    "Location";

  return (
    <div className="flex items-center space-x-2">
      <span className="text-lg">
        <RiMapPinLine />
      </span>
      <div>
        <p className="font-medium">Location</p>
        <p className="text-sm opacity-75">{content}</p>
      </div>
    </div>
  );
};

export default LocationMessage;
