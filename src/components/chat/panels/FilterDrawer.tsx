import React, { useState } from "react";
import { Button } from "@heroui/button";
import { Select, SelectItem } from "@heroui/select";

interface FilterDrawerProps {
  isOpen: boolean;
  onOpenChange: () => void;
}

const FilterDrawer: React.FC<FilterDrawerProps> = ({
  isOpen,
  onOpenChange,
}) => {
  const [selectedLabel, setSelectedLabel] = useState<string>("");
  const [selectedDepartment, setSelectedDepartment] = useState<string>("");

  return (
    <div
      aria-label="Filter drawer"
      className={`absolute inset-y-0 left-0 z-1 w-full max-w-full bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700 shadow-lg transform transition-transform duration-200 ease-out ${
        isOpen ? "translate-x-0" : "-translate-x-full"
      }`}
    >
      <div className="flex items-center justify-between p-3 border-b border-gray-200 dark:border-gray-700">
        <h2 className="text-base font-semibold text-gray-900 dark:text-white">
          Filters
        </h2>
        <Button size="sm" variant="light" onPress={onOpenChange}>
          Close
        </Button>
      </div>
      <div className="p-3">
        <Select
          className="mb-4"
          label="Label"
          placeholder="Select a label"
          selectedKeys={selectedLabel ? [selectedLabel] : []}
          onSelectionChange={(keys) =>
            setSelectedLabel(Array.from(keys)[0] as string)
          }
        >
          <SelectItem key="Work">Work</SelectItem>
          <SelectItem key="Personal">Personal</SelectItem>
          <SelectItem key="Important">Important</SelectItem>
          <SelectItem key="Archive">Archive</SelectItem>
        </Select>
        <Select
          label="Department"
          placeholder="Select a department"
          selectedKeys={selectedDepartment ? [selectedDepartment] : []}
          onSelectionChange={(keys) =>
            setSelectedDepartment(Array.from(keys)[0] as string)
          }
        >
          <SelectItem key="Sales">Sales</SelectItem>
          <SelectItem key="Marketing">Marketing</SelectItem>
          <SelectItem key="Support">Support</SelectItem>
          <SelectItem key="HR">HR</SelectItem>
          <SelectItem key="Finance">Finance</SelectItem>
        </Select>
        <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700 flex gap-2">
          <Button
            fullWidth
            color="primary"
            onPress={() => {
              // TODO: Apply filters logic
            }}
          >
            Apply Search
          </Button>
          <Button fullWidth variant="light" onPress={onOpenChange}>
            Batal
          </Button>
        </div>
      </div>
    </div>
  );
};

export default FilterDrawer;
