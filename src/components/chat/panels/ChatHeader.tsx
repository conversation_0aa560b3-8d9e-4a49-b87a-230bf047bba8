import type { ChatModel } from "@/types/firestore/chatModel";

import { But<PERSON> } from "@heroui/button";
import React from "react";
import {
  IoChevronBackOutline,
  IoInformationCircleOutline,
} from "react-icons/io5";

import { useAppDispatch, useAppSelector } from "@/store/hooks.ts";
import { setPanelVisibility, toggleRightPanel } from "@/store/uiSlice.ts";

interface ChatHeaderProps {
  chat: ChatModel;
  onBackClick?: () => void;
}

const ChatHeader: React.FC<ChatHeaderProps> = ({ chat, onBackClick }) => {
  const dispatch = useAppDispatch();
  const { screenSize, visiblePanels } = useAppSelector((state) => state.ui);

  const handleInfoClick = () => {
    if (screenSize === "desktop") {
      dispatch(
        setPanelVisibility({
          panel: "rightPanel",
          visible: !visiblePanels.rightPanel,
        }),
      );
    } else {
      // For mobile and tablet, use toggleRightPanel which will handle the overlay
      dispatch(toggleRightPanel());
    }
  };

  return (
    <div className="flex items-center justify-between p-3 border-b border-gray-200 dark:border-gray-600 bg-white dark:bg-gray-800 shadow-sm dark:shadow-black/20">
      {/* Left Section */}
      <div className="flex items-center space-x-2">
        {/* Back Button (Mobile) */}
        {onBackClick && (
          <Button
            isIconOnly
            className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
            variant="light"
            onPress={onBackClick}
          >
            <IoChevronBackOutline className="w-6 h-6" />
          </Button>
        )}

        {/* Avatar */}
        <div className="w-8 h-8 rounded-full bg-blue-500 flex items-center justify-center text-white font-medium text-sm shadow-sm">
          {chat.name.charAt(0).toUpperCase()}
        </div>

        {/* Contact Info */}
        <div className="flex-1 min-w-0">
          <div className="flex items-center space-x-1.5">
            <h2 className="font-medium text-gray-900 dark:text-white truncate text-sm">
              {chat.name}
            </h2>
          </div>

          <div className="flex items-center space-x-3">
            {/* Phone Number */}
            <span className="text-xs text-gray-500 dark:text-gray-400">
              {chat.phoneNumber}
            </span>

            {/* Label */}
            {chat.label && (
              <span className="inline-block bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 text-xs px-2 py-0.5 rounded-full">
                {chat.label.name}
              </span>
            )}
          </div>
        </div>
      </div>

      {/* Right Section - Action Buttons */}
      <div className="flex items-center space-x-1">
        {/* Info Button */}
        <Button
          isIconOnly
          className={`
            w-8 h-8
            ${
              visiblePanels.rightPanel && screenSize === "desktop"
                ? "text-blue-600 bg-blue-50 dark:text-blue-400 dark:bg-blue-900/20"
                : "text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
            }
          `}
          size="sm"
          title="Contact info"
          variant="light"
          onPress={handleInfoClick}
        >
          <IoInformationCircleOutline className="w-4 h-4" />
        </Button>

        {/* More Options */}
        {/*<Button*/}
        {/*  isIconOnly*/}
        {/*  className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 w-8 h-8"*/}
        {/*  size="sm"*/}
        {/*  title="More options"*/}
        {/*  variant="light"*/}
        {/*>*/}
        {/*  <IoEllipsisVertical className="w-4 h-4" />*/}
        {/*</Button>*/}
      </div>
    </div>
  );
};

export default ChatHeader;
