import type { ErrorObject, NormalizedMessage } from "../../../types";

import React, { memo, useState } from "react";
import dayjs from "dayjs";
import {
  RiCheckDoubleFill,
  Ri<PERSON><PERSON>ck<PERSON>ill,
  Ri<PERSON>lose<PERSON><PERSON>cle<PERSON>ill,
  RiTimeFill,
} from "react-icons/ri";
import { addToast } from "@heroui/react";

import { getInitials } from "../../../utils/stringUtils.ts";

import MessageBubbleDropdown from "./MessageBubbleDropdown";
import TextMessage from "./MessageBubble/TextMessage";
import ImageMessage from "./MessageBubble/ImageMessage";
import AudioMessage from "./MessageBubble/AudioMessage";
import VideoMessage from "./MessageBubble/VideoMessage";
import DocumentMessage from "./MessageBubble/DocumentMessage";
import LocationMessage from "./MessageBubble/LocationMessage";
import ContactMessage from "./MessageBubble/ContactMessage";
import UnsupportedMessage from "./MessageBubble/UnsupportedMessage";

import { ChatModel } from "@/types/firestore/chatModel.ts";

interface MessageBubbleProps {
  message: NormalizedMessage;
  isFirstInGroup: boolean;
  isLastInGroup: boolean;
  activeChat: ChatModel;
}

// Message status icons
const MessageStatusIcon = ({
  status,
}: {
  status: NormalizedMessage["status"];
}) => {
  const statusConfig: Partial<
    Record<
      NormalizedMessage["status"],
      {
        label: string;
        icon: React.ReactNode;
        iconClassName: string;
        pulse?: boolean;
      }
    >
  > = {
    sending: {
      label: "Sending",
      icon: <RiTimeFill className="h-4 w-4" />,
      iconClassName: "text-amber-600 dark:text-amber-400 animate-spin",
      pulse: true,
    },
    sent: {
      label: "Sent",
      icon: <RiCheckFill className="h-4 w-4" />,
      iconClassName: "text-sky-600 dark:text-sky-400",
    },
    delivered: {
      label: "Delivered",
      icon: <RiCheckDoubleFill className="h-4 w-4" />,
      iconClassName: "text-indigo-600 dark:text-indigo-400",
    },
    read: {
      label: "Read",
      icon: <RiCheckDoubleFill className="h-4 w-4" />,
      iconClassName: "text-emerald-600 dark:text-emerald-400",
    },
    failed: {
      label: "Failed",
      icon: <RiCloseCircleFill className="h-4 w-4" />,
      iconClassName: "text-rose-600 dark:text-rose-400",
    },
  };

  const config = statusConfig[status];

  if (!config) {
    return null;
  }

  return (
    <span
      className={`inline-flex items-center justify-center ${config.iconClassName} ${config.pulse ? "animate-pulse" : ""}`}
      title={config.label}
    >
      {config.icon}
    </span>
  );
};

interface ErrorDisplayProps {
  errors?: ErrorObject[];
  title: string; // "Status Errors" atau "Unsupported Message Errors"
  colorScheme: "status" | "unsupported"; // Untuk styling berbeda
}

const ErrorDisplay = ({ errors, title, colorScheme }: ErrorDisplayProps) => {
  const [showDetails, setShowDetails] = useState(false);

  if (!errors || errors.length === 0) return null;

  const colorClasses =
    colorScheme === "status"
      ? "text-rose-700 dark:text-rose-300 border-rose-200 dark:border-rose-800"
      : "text-amber-700 dark:text-amber-300 border-amber-200 dark:border-amber-800";

  return (
    <div className={`mt-2 pt-2 border-t ${colorClasses}`}>
      <div className="flex items-center justify-between">
        <p
          className={`text-xs font-medium ${colorClasses.replace("text-", "text-").replace("border-", "")}`}
        >
          {title}
        </p>
        <button
          className={`text-xs ${colorClasses.replace("text-", "text-")} hover:${colorClasses.replace("text-", "text-").replace("border-", "").replace("dark:", "dark:hover:")}`}
          onClick={() => setShowDetails(!showDetails)}
        >
          {showDetails ? "Hide" : "Details"}
        </button>
      </div>

      {showDetails && (
        <div className="mt-1 space-y-1 text-xs">
          {errors.map((error, index) => (
            <div
              key={index}
              className={`space-y-0.5 ${colorClasses.replace("text-", "text-")}`}
            >
              <div className="flex items-center justify-between">
                <span className="font-medium">{error.title}</span>
                <span className="opacity-75">({error.code})</span>
              </div>
              <p className="opacity-80">{error.message}</p>
              {error.error_data?.details && (
                <p className="opacity-70 text-xs">{error.error_data.details}</p>
              )}
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

const formatTime = (timestamp: string): string => {
  return dayjs(timestamp).format("HH:mm");
};

const MessageBubble: React.FC<MessageBubbleProps> = memo(
  ({ message, isFirstInGroup, isLastInGroup, activeChat }) => {
    const isOutgoing = message.direction === "out";
    const isOwnMessage = isOutgoing;

    const copyMessageId = async () => {
      try {
        // Prioritize whatsappMessageId if available, fall back to id
        const messageIdToCopy = message.whatsappMessageId || message.id;

        await navigator.clipboard.writeText(messageIdToCopy);
        addToast({
          title: "Success",
          description: "Message ID copied to clipboard",
          color: "success",
          timeout: 5000,
        });
      } catch {
        addToast({
          title: "Error",
          description: "Failed to copy message ID",
          color: "danger",
          timeout: 5000,
        });
      }
    };

    const renderMessageContent = () => {
      switch (message.type) {
        case "text":
        case "reaction":
        case "button_reply":
        case "list_reply":
        case "order":
        case "system":
          return <TextMessage message={message} />;
        case "image":
          return <ImageMessage message={message} />;
        case "audio":
          return <AudioMessage message={message} />;
        case "video":
          return <VideoMessage message={message} />;
        case "document":
          return <DocumentMessage message={message} />;
        case "location":
          return <LocationMessage message={message} />;
        case "contact":
          return <ContactMessage message={message} />;
        case "unsupported":
          return <UnsupportedMessage message={message} />;
        default:
          return <UnsupportedMessage message={message} />;
      }
    };

    return (
      <div
        className={`flex ${isOwnMessage ? "justify-end" : "justify-start"} ${isLastInGroup ? "mb-4" : "mb-1"}`}
      >
        <div
          className={`flex flex-col ${isOwnMessage ? "items-end" : "items-start"} max-w-xs lg:max-w-md xl:max-w-lg relative`}
        >
          <div
            className={`flex items-end w-full ${isOutgoing ? "flex-row-reverse" : "flex-row"}`}
          >
            {/* Avatar (only for contact messages and first in group) */}
            {!isOwnMessage && isFirstInGroup && (
              <div className="w-10 h-8">
                <div className="w-8 h-8 rounded-full bg-blue-500 flex items-center justify-center flex-shrink-0 shadow-sm">
                  <span className="text-white text-xs font-medium">
                    {getInitials(activeChat.name)}
                  </span>
                </div>
              </div>
            )}

            {!isOwnMessage && !isFirstInGroup && (
              <div className="w-10 h-8" /> // Spacer for alignment
            )}

            {/* Container for dropdown and message bubble */}
            <div
              className={`flex items-start ${message.direction === "out" ? "flex-row" : "flex-row-reverse"}`}
            >
              {/* Dropdown - positioned outside the bubble with proper spacing */}
              <MessageBubbleDropdown
                className={`${isOutgoing ? "mt-1" : "mt-1"}`}
                direction={message.direction}
                onAction={(key) => {
                  if (key === "copyMessageId") {
                    copyMessageId();
                  }
                }}
              />

              {/* Message Bubble */}
              <div
                className={`
              px-4 py-2 shadow-sm max-w-full
              ${
                isOwnMessage
                  ? "bg-blue-500 text-white"
                  : "bg-white dark:bg-gray-800 dark:text-white border border-gray-200 dark:border-gray-700"
              }
              ${
                isFirstInGroup && isLastInGroup
                  ? "rounded-2xl"
                  : isFirstInGroup
                    ? isOwnMessage
                      ? "rounded-2xl rounded-br-md"
                      : "rounded-2xl rounded-bl-md"
                    : isLastInGroup
                      ? isOwnMessage
                        ? "rounded-2xl rounded-tr-md"
                        : "rounded-2xl rounded-tl-md"
                      : isOwnMessage
                        ? "rounded-r-2xl rounded-l-md"
                        : "rounded-l-2xl rounded-r-md"
              }
            `}
              >
                {/* Reply indicator */}
                {message.replyTo && (
                  <div
                    className={`
                  text-xs mb-2 pb-2 border-l-2 pl-2
                  ${
                    isOwnMessage
                      ? "border-blue-300 text-blue-100"
                      : "border-gray-300 dark:border-gray-600 text-gray-500 dark:text-gray-300"
                  }
                `}
                  >
                    <p className="font-medium">{message.replyTo.sender}</p>
                    <p className="truncate">{message.replyTo.content}</p>
                  </div>
                )}

                {/* Message content */}
                <div className="text-sm">{renderMessageContent()}</div>

                {/* Status errors display */}
                {message.statusDetails?.errors && (
                  <ErrorDisplay
                    colorScheme="status"
                    errors={message.statusDetails.errors}
                    title="Status Errors"
                  />
                )}

                {/* Reactions */}
                {message.reactions && message.reactions.length > 0 && (
                  <div className="flex flex-wrap gap-1 mt-2">
                    {message.reactions.map((reaction, index) => (
                      <span
                        key={index}
                        className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-gray-100 dark:bg-gray-700"
                      >
                        {reaction.emoji}
                        <span className="ml-1 text-gray-600 dark:text-gray-300">
                          1
                        </span>
                      </span>
                    ))}
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Message metadata (timestamp and status) - Only show for last message in group */}
          {isLastInGroup && (
            <div
              className={`
              flex items-center mt-1 space-x-2 text-xs
              ${isOwnMessage ? "justify-end" : "justify-start pl-11"}
              ${isOwnMessage ? "text-blue-500 dark:text-blue-400" : "text-gray-500 dark:text-gray-400"}
            `}
            >
              {/* Forwarded indicator */}
              {message.metadata?.forwarded && (
                <span className="italic opacity-75">Forwarded</span>
              )}

              {/* Timestamp with improved positioning - only for last message in group */}
              <span className="opacity-75">
                {formatTime(message.timestamp)}
              </span>

              {/* Status (only for own messages) with better spacing */}
              {isOwnMessage && (
                <div className="flex items-center">
                  <MessageStatusIcon status={message.status} />
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    );
  },
);

MessageBubble.displayName = "MessageBubble";

export default MessageBubble;
