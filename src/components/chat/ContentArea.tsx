import React from "react";

import LeftPanel from "./panels/LeftPanel";
import CenterPanel from "./panels/CenterPanel";
import RightPanel from "./panels/RightPanel";

import { closeRightPanel, setMainMenuExpanded } from "@/store/uiSlice.ts";
import { useAppDispatch, useAppSelector } from "@/store/hooks.ts";

const ContentArea: React.FC = () => {
  const dispatch = useAppDispatch();
  const { visiblePanels, screenSize } = useAppSelector((state) => state.ui);

  // Calculate left margin - always maintain ml-14 when sidebar is visible on non-mobile screens
  const leftMargin =
    visiblePanels.mainMenu && screenSize !== "mobile" ? "ml-14" : "ml-0";

  // Check if user is in a chat room (on mobile)
  const isInChatRoom =
    screenSize === "mobile" &&
    visiblePanels.centerPanel &&
    !visiblePanels.leftPanel;

  const handleBackdropClick = () => {
    dispatch(closeRightPanel());
  };

  const handleToggleMainMenu = () => {
    dispatch(setMainMenuExpanded(true));
  };

  return (
    <div
      className={`flex h-full ${leftMargin} transition-all duration-200 ease-out`}
    >
      {/* Hamburger Menu Button - Visible only on mobile when sidebar is hidden and not in a chat room */}
      {screenSize === "mobile" && !visiblePanels.leftPanel && !isInChatRoom && (
        <button
          aria-label="Open menu"
          className="fixed top-4 left-4 z-30 p-2 rounded-md bg-white dark:bg-gray-800 shadow-md hover:bg-gray-100 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-all duration-200"
          onClick={handleToggleMainMenu}
        >
          <svg
            className="w-6 h-6 text-gray-800 dark:text-gray-200"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              d="M4 6h16M4 12h16M4 18h16"
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
            />
          </svg>
        </button>
      )}

      {/* Left Panel - Chat List */}
      {visiblePanels.leftPanel && (
        <div
          className={`
          ${screenSize === "mobile" ? "flex-1" : "w-72 flex-shrink-0"}
          ${screenSize === "tablet" ? "w-64" : ""}
        `}
        >
          <LeftPanel />
        </div>
      )}

      {/* Center Panel - Active Chat */}
      {visiblePanels.centerPanel && (
        <div
          className={`
          bg-gray-100 dark:bg-gray-950 flex-1 flex flex-col
          ${screenSize === "mobile" && !visiblePanels.leftPanel ? "w-full" : ""}
        `}
        >
          <CenterPanel />
        </div>
      )}

      {/* Right Panel - Chat Info */}
      {visiblePanels.rightPanel && (
        <>
          {screenSize === "desktop" ? (
            // Desktop: Fixed width panel
            <div className="w-72 flex-shrink-0">
              <RightPanel />
            </div>
          ) : (
            // Mobile/Tablet: Overlay panel
            <>
              {/* Backdrop */}
              <div
                className="fixed inset-0 bg-black/30 backdrop-blur-sm z-40"
                role="button"
                tabIndex={0}
                onClick={handleBackdropClick}
                onKeyDown={(e) => {
                  if (e.key === "Enter" || e.key === " ") {
                    handleBackdropClick();
                  }
                }}
              />
              {/* Right Panel Overlay */}
              <div className="fixed inset-y-0 right-0 w-80 max-w-[90vw] border-l border-gray-200 dark:border-gray-700 z-50 shadow-xl">
                <RightPanel />
              </div>
            </>
          )}
        </>
      )}
    </div>
  );
};

export default ContentArea;
