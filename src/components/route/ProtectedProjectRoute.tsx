import { Navigate, Outlet, useLocation, useParams } from "react-router-dom";
import { useEffect, useState } from "react";

import { getProject, Project } from "@/services/main/projectMainService.ts";
import { useAppDispatch } from "@/store/hooks";
import { fetchAllLabels } from "@/store/labelsSlice";

const ProtectedProjectRoute = () => {
  const location = useLocation();
  const { projectId } = useParams();
  const dispatch = useAppDispatch();
  const [project, setProject] = useState<Project | null>(null);
  const [projectLoadStatus, setProjectLoadStatus] = useState<
    "none" | "loading" | "error" | "success"
  >("none");

  useEffect(() => {
    if (!projectId) {
      return;
    }
    setProjectLoadStatus("loading");
    const fetchProject = async () => {
      try {
        const response = await getProject(projectId);

        if (response.data.data) {
          setProject(response.data.data);
          setProjectLoadStatus("success");
        }
      } catch {
        setProjectLoadStatus("error");
      }
    };

    void fetchProject();
  }, [location.pathname, projectId]);

  // Fetch labels when project is successfully loaded
  useEffect(() => {
    if (project && projectLoadStatus === "success") {
      dispatch(fetchAllLabels({ projectId: project.uid }));
    }
  }, [project, projectLoadStatus, dispatch]);

  if (!projectId || projectLoadStatus === "error") {
    return <Navigate replace={true} to="/" />;
  }

  return (
    <Outlet
      context={{
        project,
        projectLoadStatus,
      }}
    />
  );
};

export default ProtectedProjectRoute;
