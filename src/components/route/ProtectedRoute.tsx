import { Navigate, Outlet } from "react-router-dom";

import { useAuth } from "@/components/providers/AuthProvider.tsx";
import AuthLoadingScreen from "@/components/auth/AuthLoadingScreen.tsx";

const ProtectedRoute = () => {
  const auth = useAuth();

  if (auth.state === "checking") {
    return <AuthLoadingScreen />;
  }

  if (auth.state === "none") {
    return <Navigate to="/login" />;
  }

  return <Outlet />;
};

export default ProtectedRoute;
