import { Navigate, Outlet } from "react-router-dom";

import { useAuth } from "@/components/providers/AuthProvider.tsx";
import AuthLoadingScreen from "@/components/auth/AuthLoadingScreen.tsx";

const RedirectIfLoggedInRoute = () => {
  const auth = useAuth();

  if (auth.state === "checking") {
    return <AuthLoadingScreen />;
  }

  if (auth.state === "loggedIn") {
    return <Navigate to="/" />;
  }

  return <Outlet />;
};

export default RedirectIfLoggedInRoute;
