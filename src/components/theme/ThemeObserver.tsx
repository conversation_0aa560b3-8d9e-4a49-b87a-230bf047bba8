import React, { useEffect } from "react";

import { useAppSelector, useAppDispatch } from "@/store/hooks";
import { setTheme } from "@/store/uiSlice";

const ThemeObserver: React.FC = () => {
  const theme = useAppSelector((state) => state.ui.theme);
  const dispatch = useAppDispatch();

  useEffect(() => {
    // On mount, determine theme with clear priority:
    // 1. Saved theme in localStorage
    // 2. System preference
    // 3. Default ("light")
    const savedTheme = localStorage.getItem("theme");
    let effectiveTheme: "light" | "dark" = "light"; // default

    if (savedTheme === "light" || savedTheme === "dark") {
      // Use saved theme if valid
      effectiveTheme = savedTheme;
    } else {
      // Fall back to system preference
      const systemPrefersDark = window.matchMedia(
        "(prefers-color-scheme: dark)",
      ).matches;

      effectiveTheme = systemPrefersDark ? "dark" : "light";
    }

    // Update Redux state if needed
    if (effectiveTheme !== theme) {
      dispatch(setTheme(effectiveTheme));
    }

    // Apply theme to DOM
    if (effectiveTheme === "dark") {
      document.documentElement.classList.add("dark");
    } else {
      document.documentElement.classList.remove("dark");
    }
  }, []); // Empty dependency array means this runs once on mount

  useEffect(() => {
    // Apply theme to DOM when Redux theme changes
    if (theme === "dark") {
      document.documentElement.classList.add("dark");
    } else {
      document.documentElement.classList.remove("dark");
    }

    // Persist theme to localStorage
    localStorage.setItem("theme", theme);
  }, [theme]);

  return null; // This component doesn't render anything
};

export default ThemeObserver;
