import React from "react";
import { Switch } from "@heroui/switch";
import { Sun, Moon } from "lucide-react";

import { useAppSelector, useAppDispatch } from "@/store/hooks";
import { toggleTheme } from "@/store/uiSlice";

const DarkModeToggle: React.FC = () => {
  const theme = useAppSelector((state) => state.ui.theme);
  const dispatch = useAppDispatch();

  const handleToggle = () => {
    dispatch(toggleTheme());
  };

  return (
    <div className="flex items-center space-x-2 px-2 py-1">
      <Sun className="h-4 w-4 text-yellow-500" />
      <Switch
        aria-label="Toggle dark mode"
        isSelected={theme === "dark"}
        onChange={handleToggle}
      />
      <Moon className="h-4 w-4 text-blue-500" />
    </div>
  );
};

export default DarkModeToggle;
